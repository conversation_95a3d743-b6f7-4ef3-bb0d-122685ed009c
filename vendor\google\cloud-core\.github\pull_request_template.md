**PLEASE READ THIS ENTIRE MESSAGE**

Hello, and thank you for your contribution! Please note that this repository is
a read-only split of `googleapis/google-cloud-php`. As such, we are
unable to accept pull requests to this repository.

We welcome your pull request and would be happy to consider it for inclusion in
our library if you follow these steps:

* Clone the parent client library repository:

```sh
$ <NAME_EMAIL>:googleapis/google-cloud-php.git
```

* Move your changes into the correct location in that library. Library code
belongs in `Core/src`, and tests in `Core/tests`.

* Push the changes in a new branch to a fork, and open a new pull request
[here](https://github.com/googleapis/google-cloud-php).

Thanks again, and we look forward to seeing your proposed change!

The Google Cloud PHP team
