/**
 * PWA Manager - Handles PWA functionality
 */
(function () {
  "use strict";

  // Check if qPWASettings is defined
  if (typeof qPWASettings === "undefined") {
    console.error("PWA Manager: qPWASettings is not defined");
    return;
  }

  // Initialize PWA functionality
  document.addEventListener("DOMContentLoaded", function () {
    initPWA();
    // --- OFFLINE INDICATOR LOGIC START ---
    // Create offline indicator element
    const offlineBannerId = "q-pwa-offline-indicator";
    let offlineBanner = document.getElementById(offlineBannerId);
    if (!offlineBanner) {
      offlineBanner = document.createElement("div");
      offlineBanner.id = offlineBannerId;
      offlineBanner.style = [
        "display:none",
        "position:fixed",
        "top:0",
        "left:0",
        "right:0",
        "z-index:10000",
        "background:#ffefc1",
        "color:#7a5a00",
        "text-align:center",
        "padding:14px 0",
        "font-size:16px",
        "font-weight:600",
        "box-shadow:0 2px 8px rgba(0,0,0,0.08)",
        "letter-spacing:0.5px",
        "border-bottom:2px solid #ffe082",
        "transition:top 0.3s",
      ].join(";");
      offlineBanner.innerHTML =
        '<span style="margin-right:8px;">⚠️</span> You are offline. Some features may be limited.';
      document.body.appendChild(offlineBanner);
    }
    function showOfflineBanner() {
      offlineBanner.style.display = "block";
    }
    function hideOfflineBanner() {
      offlineBanner.style.display = "none";
    }
    // Initial state
    if (!navigator.onLine) {
      showOfflineBanner();
    }
    // Listen for online/offline events
    window.addEventListener("offline", showOfflineBanner);
    window.addEventListener("online", hideOfflineBanner);
    // --- OFFLINE INDICATOR LOGIC END ---
  });

  // Initialize PWA functionality
  function initPWA() {
    // Service worker is handled by firebase-init.js
    if ("serviceWorker" in navigator) {
      // Listen for messages from the service worker
      navigator.serviceWorker.addEventListener(
        "message",
        handleServiceWorkerMessage
      );
    }
  }

  // Handle messages from the service worker
  function handleServiceWorkerMessage(event) {
    if (window.location.hostname === "localhost") {
      console.log("PWA: Message received from service worker:", event.data);
    }

    if (!event.data || !event.data.type) return;

    switch (event.data.type) {
      case "CACHED_PAGES":
        // Handle cached pages list
        updateCachedPagesList(event.data.pages);
        break;

      case "PWA_UPDATE_AVAILABLE":
        // Show update notification
        showUpdateNotification();
        break;

      case "pwa_shortcut":
        // Handle PWA shortcut navigation
        if (event.data.url) {
          window.location.href = event.data.url;
        }
        break;
    }
  }

  // Show update notification
  function showUpdateNotification() {
    // Create notification element
    const notification = document.createElement("div");
    notification.className = "q-pwa-update-notification";
    notification.innerHTML = `
            <div class="q-pwa-update-content">
                <p>A new version is available!</p>
                <button id="q-pwa-update-now">Update Now</button>
                <button id="q-pwa-update-later">Later</button>
            </div>
        `;

    // Add notification to the page
    document.body.appendChild(notification);

    // Show notification with animation
    setTimeout(() => {
      notification.classList.add("show");
    }, 100);

    // Handle update now button
    document
      .getElementById("q-pwa-update-now")
      .addEventListener("click", function () {
        // Reload the page to get the new version
        window.location.reload();
      });

    // Handle update later button
    document
      .getElementById("q-pwa-update-later")
      .addEventListener("click", function () {
        // Hide notification
        notification.classList.remove("show");
        setTimeout(() => {
          notification.remove();
        }, 300);
      });
  }

  // -----------------------------------------
  // PWA Install Prompt Handling
  // -----------------------------------------

  /**
   * Basic PWA installation manager. It captures the `beforeinstallprompt` event,
   * exposes an `installApp` method and renders a simple install button that is
   * shown when the app is installable and hidden once it is installed.
   *
   * This class is intentionally lightweight – it only deals with the install
   * prompt. More advanced features live in their own dedicated scripts.
   */
  class QPWAManager {
    constructor() {
      // Detect if the app is already installed (stand-alone display mode or iOS standalone)
      this.isInstalled =
        window.matchMedia("(display-mode: standalone)").matches ||
        window.navigator.standalone === true;

      // Detect iOS Safari (no native beforeinstallprompt support)
      this.isIOS =
        /iphone|ipad|ipod/i.test(window.navigator.userAgent) &&
        !window.MSStream;

      // Detect device type
      this.deviceType = getDeviceType();

      // Will hold the `beforeinstallprompt` event until the user clicks Install
      this.deferredPrompt = null;

      // Only create the install UI if on mobile or tablet, or if desktop install is enabled
      if (
        this.deviceType === 'mobile' ||
        this.deviceType === 'tablet' ||
        this.isIOS ||
        (this.deviceType === 'desktop' && qPWASettings.installableOnDesktop)
      ) {
        this.addInstallButton();
        // Register listeners that drive the install flow
        this.registerPromptListeners();
        // On iOS, automatically display the manual banner after a tiny delay
        if (this.isIOS && !this.isInstalled) {
          setTimeout(() => this.showInstallContainer(), 1200);
        }
      }
    }

    /**
     * Registers `beforeinstallprompt` and `appinstalled` listeners.
     */
    registerPromptListeners() {
      // Fired when the browser has detected the app can be installed
      window.addEventListener("beforeinstallprompt", (e) => {
        // Prevent the automatic browser mini-infobar
        e.preventDefault();
        this.deferredPrompt = e;

        // Reveal the install banner/button to the user
        this.showInstallContainer();
        if (window.location.hostname === "localhost") {
          console.log("Q-PWA: beforeinstallprompt captured");
        }
      });

      // Fired once the app has been successfully installed
      window.addEventListener("appinstalled", () => {
        this.isInstalled = true;
        this.hideInstallContainer();
        this.deferredPrompt = null;
        if (window.location.hostname === "localhost") {
          console.log("Q-PWA: App installed – congratulations!");
        }
      });
    }

    /**
     * Injects the install button container into <body> (if it does not yet exist)
     */
    addInstallButton() {
      let container = document.getElementById("q-pwa-install-container");
      if (!container) {
        container = document.createElement("div");
        container.id = "q-pwa-install-container";
        container.className = "q-pwa-install-container hidden";
        document.body.appendChild(container);
      }
      this.updateInstallContainerContent();
    }

    /**
     * Updates the content of the install container based on device type
     */
    updateInstallContainerContent() {
      const container = document.getElementById("q-pwa-install-container");
      if (!container) return;
      // Hide container on desktop if not allowed
      if (this.deviceType === 'desktop' && !qPWASettings.installableOnDesktop) {
        container.classList.add('hidden');
        container.innerHTML = '';
        return;
      }
      if (this.deviceType === 'desktop' && qPWASettings.installableOnDesktop) {
        // Show normal install prompt for desktop
        container.innerHTML = `
          <div class="q-pwa-install-prompt" role="dialog" aria-modal="true" aria-label="Install App">
            <div class="q-pwa-install-content">
              ${qPWASettings.iconUrl ? `<img src="${qPWASettings.iconUrl}" alt="App icon" class="q-pwa-install-icon" style="width:48px;height:48px;border-radius:12px;">` : ""}
              <div class="q-pwa-install-text">
                <h3>Install our app</h3>
                <p>Get a better experience and quick access by installing our app on your desktop.</p>
              </div>
              <div class="q-pwa-install-actions">
                <button id="q-pwa-install-button" class="q-pwa-btn-primary" aria-label="Install App">Install Now</button>
                <button id="q-pwa-dismiss-button" class="q-pwa-btn-secondary" aria-label="Dismiss Install Prompt">Not Now</button>
              </div>
            </div>
          </div>
        `;
        container.querySelector("#q-pwa-install-button").addEventListener("click", () => {
          this.installApp();
        });
        container.querySelector("#q-pwa-dismiss-button").addEventListener("click", () => {
          this.hideInstallContainer();
        });
        return;
      }
      if (this.isIOS) {
        container.innerHTML = `
          <div class="q-pwa-install-prompt" role="dialog" aria-modal="true" aria-label="Install App">
            <div class="q-pwa-install-content">
              ${qPWASettings.iconUrl ? `<img src="${qPWASettings.iconUrl}" alt="App icon" class="q-pwa-install-icon" style="width:48px;height:48px;border-radius:12px;">` : ""}
              <div class="q-pwa-install-text">
                <h3>Install this app on your Device:</h3>
                <p>
                  Tap Share
                  <span aria-label="Share icon" style="font-size: 1.2em;  margin: 0 6px;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16" role="img">
                      <title>Share</title>
                      <path fill-rule="evenodd" d="M3.5 6a.5.5 0 0 0-.5.5v8a.5.5 0 0 0 .5.5h9a.5.5 0 0 0 .5-.5v-8a.5.5 0 0 0-.5-.5h-2a.5.5 0 0 1 0-1h2A1.5 1.5 0 0 1 14 6.5v8a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 14.5v-8A1.5 1.5 0 0 1 3.5 5h2a.5.5 0 0 1 0 1z"/>
                      <path fill-rule="evenodd" d="M7.646.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 1.707V10.5a.5.5 0 0 1-1 0V1.707L5.354 3.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                  </span>
                  and then select <strong>Add to Home Screen</strong>.
                </p>
              </div>
              <div class="q-pwa-install-actions">
                <button id="q-pwa-dismiss-button" class="q-pwa-btn-primary" aria-label="Dismiss Install Prompt">Dismiss</button>
              </div>
            </div>
          </div>
        `;
        container.querySelector("#q-pwa-dismiss-button").addEventListener("click", () => {
          this.hideInstallContainer();
        });
      } else {
        container.innerHTML = `
          <div class="q-pwa-install-prompt" role="dialog" aria-modal="true" aria-label="Install App">
            <div class="q-pwa-install-content">
              ${qPWASettings.iconUrl ? `<img src="${qPWASettings.iconUrl}" alt="App icon" class="q-pwa-install-icon" style="width:48px;height:48px;border-radius:12px;">` : ""}
              <div class="q-pwa-install-text">
                <h3>Install our app</h3>
                <p>Get a better experience and quick access by installing our app on your device.</p>
              </div>
              <div class="q-pwa-install-actions">
                <button id="q-pwa-install-button" class="q-pwa-btn-primary" aria-label="Install App">Install Now</button>
                <button id="q-pwa-dismiss-button" class="q-pwa-btn-secondary" aria-label="Dismiss Install Prompt">Not Now</button>
              </div>
            </div>
          </div>
        `;
        container.querySelector("#q-pwa-install-button").addEventListener("click", () => {
          this.installApp();
        });
        container.querySelector("#q-pwa-dismiss-button").addEventListener("click", () => {
          this.hideInstallContainer();
        });
      }
    }

    /**
     * Manually triggers the installation prompt if available.
     */
    installApp() {
      if (!this.deferredPrompt) {
        console.warn("Q-PWA: Install prompt not available");
        return;
      }

      // Show the prompt
      this.deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      this.deferredPrompt.userChoice.then((choiceResult) => {
        if (window.location.hostname === "localhost") {
          console.log(
            "Q-PWA: User response to the install prompt:",
            choiceResult.outcome
          );
        }
        // Clear stored event – it can only be used once
        this.deferredPrompt = null;
        if (choiceResult.outcome !== "accepted") {
          // If the user dismissed the prompt, keep the banner visible so they can try again
          this.showInstallContainer();
        }
      });
    }

    /** Show the banner/container */
    showInstallContainer() {
      if (this.isInstalled) return;
      // Only show on mobile/tablet/iOS, or if desktop install is enabled
      if (this.deviceType === 'desktop' && !qPWASettings.installableOnDesktop) return;
      this.updateInstallContainerContent();
      const el = document.getElementById("q-pwa-install-container");
      if (el) el.classList.remove("hidden");
    }

    /** Hide the banner/container */
    hideInstallContainer() {
      const el = document.getElementById("q-pwa-install-container");
      if (el) el.classList.add("hidden");
    }
  }

  // Expose manager globally so it can be accessed from other debug/helper scripts
  window.QPWAManager = QPWAManager;

  // Auto-initialize if the main PWA functionality is enabled in settings
  if (qPWASettings && qPWASettings.enabled) {
    window.qPWAManager = new QPWAManager();
  }

  // Utility: Device type detection (copied from pwa-analytics-tracking.js)
  function getDeviceType() {
    const ua = navigator.userAgent;
    if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
      return 'tablet';
    }
    if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
      return 'mobile';
    }
    return 'desktop';
  }
})();
