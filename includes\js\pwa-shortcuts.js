/**
 * PWA Shortcuts Handler
 * Handles navigation for PWA shortcuts to prevent opening in new windows
 */

(function() {
    // Check if this is a PWA shortcut request
    const urlParams = new URLSearchParams(window.location.search);
    const isPwaShortcut = urlParams.get('pwa_shortcut') === '1';
    
    // Check if this is a standalone PWA
    const isPwa = window.matchMedia('(display-mode: standalone)').matches || 
                 window.navigator.standalone === true;
    
    // If this is a PWA shortcut but not in PWA context, try to close this window
    if (isPwaShortcut && !isPwa) {
        // Clean the URL by removing the pwa_shortcut parameter
        const cleanUrl = window.location.href.replace(/[?&]pwa_shortcut=1/, '');
        
        // Try to find an existing PWA window
        if (window.opener) {
            try {
                // Try to navigate the opener
                window.opener.location.href = cleanUrl;
                // Close this window
                window.close();
            } catch (e) {
                console.error('Failed to handle PWA shortcut:', e);
                // If we can't navigate the opener, just clean our URL
                window.history.replaceState({}, document.title, cleanUrl);
            }
        } else {
            // Just clean our URL if no opener
            window.history.replaceState({}, document.title, cleanUrl);
        }
    } else if (isPwaShortcut) {
        // If we're already in the PWA, just clean the URL
        const cleanUrl = window.location.href.replace(/[?&]pwa_shortcut=1/, '');
        window.history.replaceState({}, document.title, cleanUrl);
    }
})();