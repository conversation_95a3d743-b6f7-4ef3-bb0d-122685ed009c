<?php
defined('ABSPATH') or die('No script kiddies please!');
require_once __DIR__ . '/../vendor/autoload.php'; // Adjust path if needed

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use <PERSON>reait\Firebase\Messaging\Notification;

/**
 * Get the path to the service account JSON file.
 *
 * @return string The path to the service account file.
 */
function q_get_service_account_path()
{
    // Get Firebase config from WordPress settings
    $firebase_config = get_option('q_firebase_config');

    if (empty($firebase_config)) {
        error_log("ERROR: Firebase configuration not found in WordPress settings");
        return false;
    }

    // Validate JSON
    $config = json_decode($firebase_config, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("ERROR: Firebase configuration is not valid JSON");
        return false;
    }

    // Create temporary file with config
    $temp_file = tempnam(sys_get_temp_dir(), 'firebase_config_');
    if ($temp_file === false) {
        error_log("ERROR: Could not create temporary file for Firebase config");
        return false;
    }

    file_put_contents($temp_file, $firebase_config);
    return $temp_file;
}

/**
 * Get the notification icon URL.
 *
 * @return string The URL of the notification icon.
 */
function q_get_notification_icon()
{
    // Try to get site icon URL
    $site_icon = get_site_icon_url();

    return esc_url($site_icon); // Sanitize the URL
}

function q_check_rate_limits($token, $type = 'notification')
{
    // Rate limit keys with different scopes
    $rate_limit_keys = [
        'hourly' => 'q_rate_' . md5($token) . '_hourly',
        'daily' => 'q_rate_' . md5($token) . '_daily'
    ];

    // Get current counts
    $hourly_count = (int) get_transient($rate_limit_keys['hourly']) ?: 0;
    $daily_count = (int) get_transient($rate_limit_keys['daily']) ?: 0;

    // Define limits
    $limits = [
        'notification' => ['hourly' => 30, 'daily' => 100],
        'subscription' => ['hourly' => 5, 'daily' => 10]
    ];

    // Check limits
    if (
        $hourly_count >= $limits[$type]['hourly'] ||
        $daily_count >= $limits[$type]['daily']
    ) {
        error_log("Rate limit reached for token: " . substr($token, 0, 10) . "...");
        return false;
    }

    // Increment counters
    set_transient($rate_limit_keys['hourly'], $hourly_count + 1, HOUR_IN_SECONDS);
    set_transient($rate_limit_keys['daily'], $daily_count + 1, DAY_IN_SECONDS);

    return true;
}

/**
 * Get cached Firebase Messaging instance
 *
 * @return Messaging|null Firebase Messaging instance or null on failure
 */
function q_get_firebase_messaging()
{
    static $messaging = null;

    if ($messaging !== null) {
        return $messaging;
    }

    $service_account = get_option('q_firebase_service_account');
    if (empty($service_account)) {
        error_log("ERROR: Firebase service account not found in settings");
        return null;
    }

    try {
        // Create a temporary file with the service account JSON
        $temp_file = tempnam(sys_get_temp_dir(), 'firebase_sa_');
        if ($temp_file === false) {
            throw new Exception("Could not create temporary file for service account");
        }
        
        file_put_contents($temp_file, $service_account);
        
        $factory = (new Kreait\Firebase\Factory)
            ->withServiceAccount($temp_file);
        $messaging = $factory->createMessaging();
        
        // Clean up the temporary file
        unlink($temp_file);
        
        return $messaging;
    } catch (Exception $e) {
        error_log("Failed to initialize Firebase Messaging: " . $e->getMessage());
        return null;
    }
}

/**
 * Send a push notification to a specific token.
 *
 * @param string $token The recipient's device token.
 * @param string $title The title of the notification.
 * @param string $message The message of the notification.
 * @param string $image Optional image for the notification.
 * @param bool $debug Optional flag to enable debug logging.
 * @param string $type Type of notification.
 * @param array $additional_data Additional data for the notification including:
 *        - icon: Custom notification icon
 *        - actions: Array of action buttons
 *        - video_thumbnail: URL to video thumbnail
 *        - gif_url: URL to GIF animation
 *        - audio_url: URL to audio file
 *        - large_image: URL to large image for big picture style
 *        - carousel_images: Array of image URLs for carousel style
 *        - progress: Array with 'current' and 'max' values for progress bar
 *        - map_location: Array with 'lat', 'lng', and 'zoom' for location thumbnail
 * @return bool True if the notification was sent successfully, false otherwise.
 */
function q_send_push_notification($token, $title, $message, $image = '', $debug = false, $type = 'info', $additional_data = [])
{
    if (!q_check_rate_limits($token, 'notification')) {
        return false;
    }

    try {
        // Validate inputs
        if (empty($token) || empty($title) || empty($message)) {
            error_log("ERROR: Missing required parameters in q_send_push_notification");
            return false;
        }
        
        // Validate token format
        if (!preg_match('/^[a-zA-Z0-9:_-]{20,300}$/', $token)) {
            error_log("ERROR: Invalid FCM token format: " . substr($token, 0, 10) . "...");
            return false;
        }

        $messaging = q_get_firebase_messaging();
        if (!$messaging) {
            return false;
        }

        // Sanitize inputs
        $title = sanitize_text_field($title);
        $message = sanitize_textarea_field($message);

        // Get custom icon if provided, otherwise don't use an icon
        $icon = !empty($additional_data['icon']) ? esc_url($additional_data['icon']) : null;

        // Use provided image or fallback to null (don't use icon as fallback)
        $notification_image = !empty($image) ? esc_url($image) : null;

        // Use custom notification_url if provided, otherwise fallback to site_url()
        $redirect_url = !empty($additional_data['notification_url']) ? $additional_data['notification_url'] : site_url();
        // Debug log
        error_log('DEBUG: Notification URL used in q_send_push_notification: ' . $redirect_url);

        // Generate a unique message ID for deduplication
        $message_id = md5($token . $title . $message . time());

        // Generate a unique notification ID
        $notification_id = uniqid('notify_', true);

        // Include form_id in the notification data if available
        $form_id = isset($additional_data['form_id']) ? $additional_data['form_id'] : null;

        // Prepare data payload with all notification information
        $data = [
            'title' => $title,
            'body' => $message,
            'image' => $notification_image,
            'timestamp' => (string) time(),
            'message_id' => $message_id,
            'notification_id' => $notification_id,
            'form_id' => $form_id ? (string) $form_id : '',
            'click_action' => $redirect_url
        ];
        
        // Only add icon if provided
        if ($icon) {
            $data['icon'] = $icon;
            $data['badge'] = $icon;
        }

        // Add rich media elements if provided
        
        // Large image / Big picture style
        if (!empty($additional_data['large_image'])) {
            $data['large_image'] = esc_url($additional_data['large_image']);
            $data['style'] = 'big-picture';
        }
        
        // Video thumbnail
        if (!empty($additional_data['video_thumbnail'])) {
            $data['video_thumbnail'] = esc_url($additional_data['video_thumbnail']);
            $data['video_url'] = !empty($additional_data['video_url']) ? esc_url($additional_data['video_url']) : '';
        }
        
        // GIF animation
        if (!empty($additional_data['gif_url'])) {
            $data['gif_url'] = esc_url($additional_data['gif_url']);
        }
        
        // Audio
        if (!empty($additional_data['audio_url'])) {
            $data['audio_url'] = esc_url($additional_data['audio_url']);
            $data['audio_title'] = !empty($additional_data['audio_title']) ? sanitize_text_field($additional_data['audio_title']) : '';
        }
        
        // Carousel images
        if (!empty($additional_data['carousel_images']) && is_array($additional_data['carousel_images'])) {
            $carousel_images = array_map('esc_url', array_filter($additional_data['carousel_images']));
            if (!empty($carousel_images)) {
                $data['carousel_images'] = json_encode($carousel_images);
                $data['style'] = 'carousel';
            }
        }
        
        // Progress bar
        if (!empty($additional_data['progress']) && is_array($additional_data['progress'])) {
            if (isset($additional_data['progress']['current']) && isset($additional_data['progress']['max'])) {
                $data['progress_current'] = (int)$additional_data['progress']['current'];
                $data['progress_max'] = (int)$additional_data['progress']['max'];
                $data['progress_indeterminate'] = !empty($additional_data['progress']['indeterminate']) ? 'true' : 'false';
                $data['style'] = 'progress';
            }
        }
        
        // Map location
        if (!empty($additional_data['map_location']) && is_array($additional_data['map_location'])) {
            if (isset($additional_data['map_location']['lat']) && isset($additional_data['map_location']['lng'])) {
                $data['map_lat'] = (float)$additional_data['map_location']['lat'];
                $data['map_lng'] = (float)$additional_data['map_location']['lng'];
                $data['map_zoom'] = isset($additional_data['map_location']['zoom']) ? (int)$additional_data['map_location']['zoom'] : 14;
                $data['style'] = 'map';
            }
        }

        // Add action buttons if provided
        // if (!empty($additional_data['actions'])) {
        //     $data['has_actions'] = 'true';
            
        //     foreach ($additional_data['actions'] as $index => $action) {
        //         $data['action_' . $action['action'] . '_title'] = $action['title'];
        //         $data['action_' . $action['action'] . '_url'] = $action['url'];
                
        //         // Add icon to action button if provided
        //         if (!empty($action['icon'])) {
        //             $data['button_' . $index . '_icon'] = $action['icon'];
        //         }
        //     }
        // }

        // Add retrigger information if provided in additional_data
        if (isset($additional_data['enable_retrigger']) && $additional_data['enable_retrigger']) {
            $data['enable_retrigger'] = 'true';
            $data['retrigger_delay'] = (string) (isset($additional_data['retrigger_delay']) ?
                intval($additional_data['retrigger_delay']) : 24);
            $data['retrigger_max_attempts'] = (string) (isset($additional_data['retrigger_max_attempts']) ?
                intval($additional_data['retrigger_max_attempts']) : 3);
            $data['retrigger_attempt'] = (string) (isset($additional_data['retrigger_attempt']) ?
                intval($additional_data['retrigger_attempt']) : 0);
            $data['action_id'] = (string) (isset($additional_data['action_id']) ?
                intval($additional_data['action_id']) : 0);

            // Store retrigger metadata
            if (isset($additional_data['is_retrigger']) && $additional_data['is_retrigger']) {
                $data['is_retrigger'] = 'true';
                $data['original_notification_id'] = $additional_data['original_notification_id'];
                $data['last_retrigger_time'] = (string) (isset($additional_data['last_retrigger_time']) ?
                    intval($additional_data['last_retrigger_time']) : time());
            }
        }

        // Structure the message using both notification and data payload for better delivery
        $payload = [
            // Remove top-level 'notification' field to prevent FCM from auto-displaying a generic notification.
            'data' => $data,
            'token' => $token,
            // Android-specific config (only relevant if token belongs to Android app)
            'android' => [
                'priority' => 'high',
                'notification' => [
                    'channel_id' => 'default',
                    'click_action' => $redirect_url,
                    'sound' => 'default',
                    'notification_count' => isset($additional_data['badge']) ? intval($additional_data['badge']) : 1,
                    'title' => $title,
                    'body' => $message,
                    'image' => $notification_image
                ]
            ],
            // iOS (APNS) configuration
            'apns' => [
                'headers' => [
                    'apns-priority' => '10'
                ],
                'payload' => [
                    'aps' => [
                        'alert' => [
                            'title' => $title,
                            'body' => $message
                        ],
                        'badge' => isset($additional_data['badge']) ? intval($additional_data['badge']) : 1,
                        'sound' => 'default',
                        'content-available' => 1,
                        'mutable-content' => 1
                    ]
                ]
            ],
            // Web push config (data-only, service worker will display)
            'webpush' => [
                'headers' => [
                    'TTL' => '86400'
                ],
                'fcm_options' => [
                    'link' => $redirect_url
                ]
            ]
        ];
        
        // Only add icon to payloads if provided
        if ($icon) {
            $payload['android']['notification']['icon'] = $icon;
        }
        
        // Add rich media to platform-specific payloads
        
        // Big picture style for Android - move to data payload instead of notification object
        if (!empty($data['large_image']) || !empty($data['image'])) {
            $big_picture = !empty($data['large_image']) ? $data['large_image'] : $data['image'];
            // Add to data payload instead of notification object
            $data['android_style'] = 'BIGPICTURE';
            $data['android_image'] = $big_picture;
        }
        
        // For iOS, add media attachments
        if (!empty($data['image']) || !empty($data['large_image']) || !empty($data['video_thumbnail']) || !empty($data['gif_url'])) {
            // Use the most appropriate image in order of preference
            $media_url = !empty($data['large_image']) ? $data['large_image'] : 
                        (!empty($data['image']) ? $data['image'] : 
                        (!empty($data['video_thumbnail']) ? $data['video_thumbnail'] : 
                        (!empty($data['gif_url']) ? $data['gif_url'] : '')));
            
            if (!empty($media_url)) {
                // Add mutable-content flag to allow media attachments
                $payload['apns']['payload']['aps']['mutable-content'] = 1;
                
                // Add media attachment URL
                $payload['apns']['fcm_options'] = [
                    'image' => $media_url
                ];
            }
        }
        
        // Add custom sound if provided
        if (!empty($additional_data['sound'])) {
            $sound = sanitize_text_field($additional_data['sound']);
            $payload['android']['notification']['sound'] = $sound;
            $payload['apns']['payload']['aps']['sound'] = $sound;
        }
        
        // Add action buttons if provided
        // if (!empty($additional_data['actions'])) {
        //     // For Android
        //     $payload['android']['notification']['click_action'] = 'OPEN_ACTIVITY_1';
        //     $payload['android']['notification']['default_sound'] = true;
            
        //     // Add action buttons for Android - using the correct format for FCM
        //     if (count($additional_data['actions']) > 0) {
        //         // Android doesn't support custom fields directly in notification object
        //         // Instead, we'll add them to the data payload
        //         $data['notification_count'] = (string)count($additional_data['actions']);
                
        //         foreach ($additional_data['actions'] as $index => $action) {
        //             $button_key = $index + 1;
        //             $data['button_' . $button_key . '_text'] = $action['title'];
        //             $data['button_' . $button_key . '_action'] = $action['action'];
                    
        //             if (!empty($action['icon'])) {
        //                 $data['button_' . $button_key . '_icon'] = $action['icon'];
        //             }
        //         }
        //     }
            
        //     // For APNS (iOS)
        //     $payload['apns']['payload']['aps']['category'] = 'NOTIFICATION_CATEGORY';
            
        //     // Add custom action category for iOS
        //     $category_id = 'CATEGORY_' . strtoupper(substr(md5($notification_id), 0, 8));
        //     $payload['apns']['payload']['aps']['category'] = $category_id;
            
        //     // Add category info to payload for client-side handling
        //     $payload['apns']['payload']['categoryId'] = $category_id;
        //     $payload['apns']['payload']['actions'] = array_map(function($action) {
        //         return [
        //             'id' => $action['action'],
        //             'title' => $action['title']
        //         ];
        //     }, $additional_data['actions']);
        // }

        // After performing all data manipulations, re-assign updated data back to payload
        $payload['data'] = $data;

        // Allow third-party code to adjust the payload before it is sent.
        $payload = apply_filters('q_push_notification_payload', $payload, $data, $additional_data);

        if ($debug) {
            error_log("Sending message payload: " . print_r($payload, true));
        }

        $response = $messaging->send(CloudMessage::fromArray($payload));

        if ($debug) {
            error_log("Firebase Response: " . print_r($response, true));
        }

        // Track notification if sent successfully
        if (!empty($response) && is_string($response['name'])) {
            // Prepare tracking data
            $tracking_data = [
                'title'   => $title,
                'message' => $message,
                'token'   => substr($token, 0, 10) . '...',
                'type'    => $type,
                // Rich media fields for inbox rendering
                'icon'            => $icon,
                'image'           => $notification_image,
                'large_image'     => isset( $additional_data['large_image'] ) ? esc_url( $additional_data['large_image'] ) : '',
                'video_thumbnail' => isset( $additional_data['video_thumbnail'] ) ? esc_url( $additional_data['video_thumbnail'] ) : '',
                'gif_url'         => isset( $additional_data['gif_url'] ) ? esc_url( $additional_data['gif_url'] ) : '',
                'audio_url'       => isset( $additional_data['audio_url'] ) ? esc_url( $additional_data['audio_url'] ) : '',
                'actions'         => ! empty( $additional_data['actions'] ) ? $additional_data['actions'] : [],
            ];

            // Add retrigger information to tracking data if available
            if (isset($payload['data']['enable_retrigger']) && $payload['data']['enable_retrigger'] === 'true') {
                $tracking_data['enable_retrigger'] = true;
                $tracking_data['retrigger_delay'] = $payload['data']['retrigger_delay'];
                $tracking_data['retrigger_max_attempts'] = $payload['data']['retrigger_max_attempts'];
                $tracking_data['retrigger_attempt'] = $payload['data']['retrigger_attempt'];
                $tracking_data['action_id'] = $payload['data']['action_id'];

                // Include retrigger metadata if this is a retrigger notification
                if (isset($payload['data']['is_retrigger']) && $payload['data']['is_retrigger'] === 'true') {
                    $tracking_data['is_retrigger'] = true;
                    $tracking_data['original_notification_id'] = $payload['data']['original_notification_id'];
                    $tracking_data['last_retrigger_time'] = $payload['data']['last_retrigger_time'];
                }
            }

            // Track the sent notification
            q_track_notification($notification_id, 'sent', $tracking_data, $form_id);

            return true;
        } else {
            error_log("Firebase notification failed: Invalid response format - " . print_r($response, true));
            return false;
        }

    } catch (Exception $e) {
        error_log("ERROR in q_send_push_notification: " . $e->getMessage());
        if ($debug) {
            error_log("Stack trace: " . $e->getTraceAsString());
        }
        return false;
    }
}

/**
 * Send multiple push notifications in batch
 *
 * @param array $notifications Array of notification data
 * @param bool $debug Optional flag to enable debug logging
 * @return array Array of results with token as key and success status as value
 */
function q_send_batch_notifications($notifications, $debug = false)
{
    $results = [];

    foreach ($notifications as $notification) {
        $token  = $notification['token'];
        $title  = $notification['title'];
        $body   = $notification['message'];
        $image  = isset($notification['image']) ? $notification['image'] : '';
        $type   = isset($notification['type']) ? $notification['type'] : 'info';
        $additional_data = isset($notification['additional_data']) ? $notification['additional_data'] : [];

        $results[$token] = q_send_push_notification(
            $token,
            $title,
            $body,
            $image,
            $debug,
            $type,
            $additional_data
        );
    }

    return $results;
}

/**
 * Retry failed notification with exponential backoff
 *
 * @param array $notification Notification data
 * @param int $maxAttempts Maximum number of retry attempts
 * @param int $attempt Current attempt number
 * @return bool Success status
 */
function q_retry_failed_notification($notification, $maxAttempts = 3, $attempt = 1)
{
    if ($attempt > $maxAttempts) {
        error_log("Max retry attempts reached for notification to token: " . substr($notification['token'], 0, 10) . "...");
        return false;
    }

    // Exponential backoff
    $delay = pow(2, $attempt - 1);
    sleep($delay);

    $success = q_send_push_notification(
        $notification['token'],
        $notification['title'],
        $notification['message'],
        isset($notification['image']) ? $notification['image'] : '',
        false,
        isset($notification['type']) ? $notification['type'] : 'info',
        isset($notification['additional_data']) ? $notification['additional_data'] : []
    );

    if ($success) {
        return true;
    }

    // Recurse
    return q_retry_failed_notification($notification, $maxAttempts, $attempt + 1);
}

/**
 * Get user ID by FCM token.
 *
 * @param string $token The FCM token.
 * @return int|false User ID on success, false on failure.
 */
function q_get_user_id_by_token($token)
{
    global $wpdb;
    $user_id = $wpdb->get_var($wpdb->prepare(
        "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = 'q_push_token' AND meta_value = %s",
        $token
    ));

    if ($user_id) {
        return (int) $user_id;
    }

    return false;
}