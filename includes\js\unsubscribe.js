import { getToken, deleteToken } from "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js";
import { messagingPromise } from "./firebase-init.js";

/**
 * Utility: verify presence of security nonce.
 */
const verifyNonce = () => {
    if (!window.q_ajax_object?.nonce) {
        console.error('Security token not found');
        return false;
    }
    return true;
};

/**
 * Show a lightweight notification message on screen.
 * Mimics the helper used in subscription.js to maintain a consistent UX.
 */
function showNotification(type = 'success', message = '') {
    // Remove any existing messages
    document.querySelectorAll('.q-notification').forEach(el => el.remove());

    const notification = document.createElement('div');
    notification.className = `q-notification q-notification-${type}`;

    // Basic sanitisation – escape HTML entities
    const safeMessage = message
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

    notification.innerHTML = `
        <div class="q-notification-content">
            <i class="bi ${type === 'error' ? 'bi-exclamation-circle' : 'bi-check-circle'}"></i>
            <span>${safeMessage}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Click-to-dismiss
    notification.addEventListener('click', () => notification.remove());

    // Auto dismiss
    setTimeout(() => {
        if (document.body.contains(notification)) notification.remove();
    }, 5000);
}

/**
 * Main logic – executed once DOM is ready and Firebase messaging initialised.
 */
const initUnsubscribe = () => {
    const btn = document.getElementById('q-unsubscribe-button');
    if (!btn) return; // shortcode not present

    messagingPromise.then(async (messaging) => {

        btn.addEventListener('click', async () => {
            if (!verifyNonce()) return;

            btn.disabled = true;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

            try {
                // Fetch current token (required both for server & deleteToken)
                const currentToken = await getToken(messaging, {
                    vapidKey: window.q_firebase_config.publicVapidKey
                });

                if (!currentToken) {
                    throw new Error('No active subscription found.');
                }

                // Notify WP to remove token from DB
                const response = await jQuery.ajax({
                    url: window.q_ajax_object.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'q_unsubscribe',
                        token: currentToken,
                        security: window.q_ajax_object.nonce
                    },
                    dataType: 'json'
                });

                if (!response?.success) {
                    throw new Error(response?.data?.message || 'Server refused the request');
                }

                // Delete token from FCM so that it won't receive further pushes
                try {
                    await deleteToken(messaging, currentToken);
                } catch (e) {
                    // Non-fatal – user will still be removed server-side
                    console.warn('Failed to delete token from FCM', e);
                }

                showNotification('success', 'Notifications disabled successfully!');
                btn.style.display = 'none';

                // Refresh page after a brief delay so UI (e.g., inbox) updates accordingly
                setTimeout(() => {
                    window.location.reload();
                }, 800);
            } catch (error) {
                console.error('Unsubscribe error', error);
                showNotification('error', error.message || 'Failed to disable notifications');
                btn.disabled = false;
                btn.innerHTML = '<i class="bi bi-bell-slash-fill"></i> Disable Notifications';
            }
        });
    }).catch(err => {
        console.error('Failed to initialise Firebase messaging', err);
        btn.style.display = 'none';
    });
};

document.addEventListener('DOMContentLoaded', initUnsubscribe); 