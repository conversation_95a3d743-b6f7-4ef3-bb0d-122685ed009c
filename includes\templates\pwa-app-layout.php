<?php
if (!defined('ABSPATH')) {
    exit;
}

// Check user capabilities
if (!current_user_can('manage_options')) {
    return;
}

// Get PWA status
$pwa_status = Q_PWA_Settings::get_pwa_status();
$manifest_validation = Q_PWA_Manifest::validate_manifest();


?>

<div class="q-pwa-app">
    <!-- Header -->
    <header class="q-pwa-header">
        <h1>PWA Settings</h1>
    </header>

    <!-- Main Content Area -->
    <div class="q-pwa-content">
        <!-- Left Sidebar -->
        <aside class="q-pwa-sidebar">
            <nav class="q-pwa-tabs-nav">
                <button class="q-pwa-tab-button active" data-tab="general">
                    <span class="dashicons dashicons-admin-generic"></span>
                    General Settings
                </button>
                <button class="q-pwa-tab-button" data-tab="images">
                    <span class="dashicons dashicons-format-image"></span>
                    Images & Splash Screens
                </button>
                <button class="q-pwa-tab-button" data-tab="ios">
                    <span class="dashicons dashicons-smartphone"></span>

                    iOS Settings
                </button>
                <button class="q-pwa-tab-button" data-tab="android">
                    <span class="dashicons dashicons-smartphone"></span>
                    Android Settings
                </button>
                <button class="q-pwa-tab-button" data-tab="desktop">
                    <span class="dashicons dashicons-desktop"></span>
                    Desktop Settings
                </button>
                <button class="q-pwa-tab-button" data-tab="advanced">
                    <span class="dashicons dashicons-admin-tools"></span>
                    Advanced Features
                </button>
                <button class="q-pwa-tab-button" data-tab="analytics">
                    <span class="dashicons dashicons-chart-bar"></span>
                    Analytics
                </button>
                <button class="q-pwa-tab-button" data-tab="shortcodes">
                    <span class="dashicons dashicons-editor-code"></span>
                    Shortcodes
                </button>

                <button class="q-pwa-tab-button" data-tab="subscribers">
                    <span class="dashicons dashicons-groups"></span>
                    Subscribers
                </button>

                <button class="q-pwa-tab-button" data-tab="notifications">
                    <span class="dashicons dashicons-bell"></span>
                    Notifications
                </button>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="q-pwa-main">
            <!-- Settings Form -->
            <form action="options.php" method="post" id="q-pwa-settings-form">
                <?php settings_fields('q_pwa_settings'); ?>

                <!-- Required for options.php to process the request -->
                <input type="hidden" name="action" value="update" />

                <!-- Tab Content -->
                <?php include_once Q_PLUGIN_DIR . 'includes/templates/pwa-tab-content.php'; ?>

                <!-- Save Button -->
                <div class="q-pwa-actions">
                    <?php submit_button('Save PWA Settings', 'primary', 'submit', false); ?>
                </div>
            </form>
        </main>

        <!-- Right Sidebar - Overview -->
        <aside class="q-pwa-overview">
            <div class="q-pwa-overview-section">
                <h3>PWA Status</h3>
                <div class="q-pwa-status-summary">
                    <div class="status-badge <?php echo $pwa_status['ready'] ? 'ready' : 'incomplete'; ?>">
                        <?php echo $pwa_status['ready'] ? 'Ready' : 'Incomplete'; ?>
                    </div>
                    <p><?php echo esc_html($pwa_status['percentage']); ?>% Complete</p>
                </div>

                <div class="q-pwa-requirements">
                    <h4>Requirements Checklist</h4>
                    <ul>
                        <li class="<?php echo $pwa_status['requirements']['https'] ? 'completed' : 'pending'; ?>">
                            <span class="dashicons <?php echo $pwa_status['requirements']['https'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                            HTTPS Enabled
                        </li>
                        <li class="<?php echo $pwa_status['requirements']['manifest'] ? 'completed' : 'pending'; ?>">
                            <span class="dashicons <?php echo $pwa_status['requirements']['manifest'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                            PWA Enabled
                        </li>
                        <li class="<?php echo $pwa_status['requirements']['service_worker'] ? 'completed' : 'pending'; ?>">
                            <span class="dashicons <?php echo $pwa_status['requirements']['service_worker'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                            Service Worker
                        </li>
                        <li class="<?php echo $pwa_status['requirements']['icons'] ? 'completed' : 'pending'; ?>">
                            <span class="dashicons <?php echo $pwa_status['requirements']['icons'] ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                            App Icons
                        </li>
                    </ul>
                </div>

                <?php if (!$manifest_validation['valid']): ?>
                    <div class="notice notice-error inline" style="margin-top: 10px;">
                        <p><strong>Manifest Errors:</strong> <?php echo esc_html(count($manifest_validation['errors'])); ?> issues found</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="q-pwa-overview-section">
                <h3>Quick Actions</h3>
                <div class="q-pwa-quick-actions">
                    <button class="button" id="q-test-manifest">
                        Test Manifest
                    </button>
                    <button class="button" id="q-preview-pwa">
                        View PWA
                    </button>
                    <a href="<?php echo esc_url(Q_PWA_Manifest::get_manifest_url()); ?>" target="_blank" class="button">
                        View Manifest
                    </a>
                    <a href="https://web.dev/measure/" target="_blank" class="button">
                        Test with Lighthouse
                    </a>

                </div>
            </div>
        </aside>
    </div>

    <!-- Footer -->
    <footer class="q-pwa-footer">
        <p>Powered by Q-Ai</p>
    </footer>
</div>

<script>
    jQuery(document).ready(function($) {
        // Simple tab switching
        $('.q-pwa-tab-button').on('click', function(e) {
            e.preventDefault();

            const tabId = $(this).data('tab');

            // Remove active class from all tabs and buttons
            $('.q-pwa-tab-button').removeClass('active');
            $('.q-pwa-tab-content').removeClass('active');

            // Add active class to clicked button and corresponding tab
            $(this).addClass('active');
            $('#tab-' + tabId).addClass('active');
        });

        // Handle shortcuts configuration visibility
        function toggleShortcutsConfig() {
            const shortcutsEnabled = $('input[name="q_pwa_shortcuts_enabled"]').is(':checked');
            $('#q-pwa-shortcuts-config').toggle(shortcutsEnabled);
        }

        // Initialize shortcuts config visibility
        toggleShortcutsConfig();

        // Listen for shortcuts checkbox changes
        $('input[name="q_pwa_shortcuts_enabled"]').on('change', toggleShortcutsConfig);
    });
</script>