/**
 * PWA Badge Handler
 *
 * Handles badge API for notification counts on the app icon
 */

(function () {
    // Initialize badge count from localStorage or set to 0
    let badgeCount = parseInt(localStorage.getItem('q_notification_badge_count') || '0');

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initBadge);

    // Firebase messaging promise
    let messagingPromise;

    // Initialize Firebase messaging if available
    async function getFirebaseMessaging() {
        if (typeof messagingPromise === 'undefined') {
            try {
                // Dynamically import Firebase messaging
                const { messaging } = await import("https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js");
                messagingPromise = messaging;
                return messaging;
            } catch (error) {
                console.error('Failed to load Firebase messaging:', error);
                return null;
            }
        }
        return messagingPromise;
    }

    /**
     * Initialize badge functionality
     */
    function initBadge() {
        // Check if badge API is enabled in settings
        if (!window.qPwaBadge || !qPwaBadge.enabled) {
            return;
        }

        // Set initial badge count from localStorage or settings
        badgeCount = parseInt(localStorage.getItem('q_notification_badge_count')) || qPwaBadge.initialCount || 0;

        // Update badge on load
        updateBadge(badgeCount);

        // Listen for notification events
        listenForNotifications();

        // Initialize Firebase messaging if available
        if (typeof messagingPromise !== 'undefined') {
            initFirebaseMessaging();
        }

        // Expose badge API
        window.qPwaBadgeAPI = {
            setBadge,
            clearBadge,
            incrementBadge,
            decrementBadge,
            getBadgeCount: () => badgeCount
        };
    }

    /**
     * Initialize Firebase Messaging for badge updates
     */
    async function initFirebaseMessaging() {
        try {
            const messaging = await getFirebaseMessaging();
            if (!messaging) return;

            // Listen for foreground messages to update badge
            const { onMessage } = await import("https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js");
            onMessage(messaging, (payload) => {
                if (window.location.hostname === 'localhost') {
                    console.log('Badge handler received message:', payload);
                }
                incrementBadge();
            });
        } catch (error) {
            console.error('Failed to initialize Firebase messaging for badge:', error);
        }
    }

    /**
     * Update badge count on the app icon
     */
    function updateBadge(count) {
        badgeCount = count;

        // Store the count in localStorage for persistence
        localStorage.setItem('q_notification_badge_count', badgeCount.toString());

        // Update badge via service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(registration => {
                if (registration.active) {
                    registration.active.postMessage({
                        type: 'SET_BADGE',
                        count: count
                    });
                }
            });
        }

        // Update badge via Badging API if available
        if ('setAppBadge' in navigator) {
            if (count > 0) {
                navigator.setAppBadge(count).catch(error => {
                    console.error('Error setting app badge:', error);
                });
            } else {
                navigator.clearAppBadge().catch(error => {
                    console.error('Error clearing app badge:', error);
                });
            }
        }

        // Dispatch event for other parts of the app
        document.dispatchEvent(new CustomEvent('q_badge_updated', {
            detail: { count: badgeCount }
        }));

        // Save badge count to server
        saveBadgeCount(count);

        return badgeCount;
    }

    /**
     * Set badge count
     */
    function setBadge(count) {
        updateBadge(parseInt(count) || 0);
    }

    /**
     * Clear badge count
     */
    function clearBadge() {
        updateBadge(0);
    }

    /**
     * Increment badge count
     */
    function incrementBadge(amount = 1) {
        updateBadge(badgeCount + amount);
    }

    /**
     * Decrement badge count
     */
    function decrementBadge(amount = 1) {
        const newCount = Math.max(0, badgeCount - amount);
        updateBadge(newCount);
    }

    /**
     * Save badge count to server
     */
    function saveBadgeCount(count) {
        if (!qPwaBadge.ajaxurl) return;

        fetch(qPwaBadge.ajaxurl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                action: 'q_pwa_set_badge',
                security: qPwaBadge.security || '',
                count: count
            })
        })
            .then(response => response.json())
            .catch(error => {
                console.error('Failed to save badge count:', error);
            });
    }

    /**
     * Listen for notification events
     */
    function listenForNotifications() {
        // Listen for new notifications
        document.addEventListener('q-notification-received', event => {
            incrementBadge();
        });

        // Listen for notification clicks
        document.addEventListener('q-notification-clicked', event => {
            decrementBadge();
        });

        // Listen for service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', event => {
                if (event.data && event.data.type === 'NOTIFICATION_CLICKED') {
                    decrementBadge();
                } else if (event.data && event.data.type === 'BADGE_UPDATED') {
                    // Sync badge count sent from service worker
                    const count = parseInt(event.data.count || 0);
                    setBadge(count);
                }
            });
        }

        // Clear badge when user interacts with the page
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                clearBadge();
            }
        });
    }
})();