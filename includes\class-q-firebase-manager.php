<?php
/**
 * Firebase Manager Class
 * This class provides a clean interface for sending push notifications
 */

class Q_Firebase_Manager {
    /**
     * Send a push notification with proper Android payload structure
     *
     * @param string $token The recipient's device token
     * @param string $title The title of the notification
     * @param string $message The message of the notification
     * @param string $image Optional image for the notification
     * @param array $options Additional options for the notification
     * @return bool True if the notification was sent successfully, false otherwise
     */
    public static function send_notification($token, $title, $message, $image = '', $options = []) {
        // Set default options
        $default_options = [
            'debug' => false,
            'type' => 'info',
            'icon' => null,
            'actions' => [],
            'large_image' => null,
            'video_url' => null,
            'video_thumbnail' => null,
            'gif_url' => null,
            'audio_url' => null,
            'audio_title' => null,
            'form_id' => null
        ];
        
        // Merge with provided options
        $options = array_merge($default_options, $options);
        
        // Extract options for the original function
        $debug = $options['debug'];
        $type = $options['type'];
        
        // Build additional_data array
        $additional_data = [];
        foreach ($options as $key => $value) {
            if ($key !== 'debug' && $key !== 'type' && !is_null($value)) {
                $additional_data[$key] = $value;
            }
        }
        
        // Always use the canonical function. It now contains the full implementation and is filter-extensible.
        if (function_exists('q_send_push_notification')) {
            return q_send_push_notification($token, $title, $message, $image, $debug, $type, $additional_data);
        }
        
        // No function available
        // error_log("ERROR: q_send_push_notification function not found");
        return false;
    }
    
    /**
     * Invalidate a token
     *
     * @param int $user_id The user ID
     * @return bool True if the token was invalidated successfully, false otherwise
     */
    public static function invalidate_token($user_id) {
        // Remove the token from the user meta
        $deleted = delete_user_meta($user_id, 'q_push_token');

        // Optionally, log the invalidation
        if ($deleted) {
            error_log("FCM token invalidated for user ID: " . $user_id);
        } else {
            error_log("Failed to invalidate FCM token for user ID: " . $user_id . ". Token might not exist.");
        }

        return $deleted;
    }

    /**
     * Periodically check and remove invalid or expired FCM tokens.
     * This function should be scheduled to run regularly (e.g., daily or weekly).
     */
    public static function cleanup_invalid_tokens() {
        // Get all users with a push token
        $users_with_tokens = get_users([
            'meta_key' => 'q_push_token',
            'fields' => ['ID']
        ]);

        if (empty($users_with_tokens)) {
            error_log("No users with FCM tokens found for cleanup.");
            return;
        }

        $messaging = q_get_firebase_messaging();
        if (!$messaging) {
            error_log("Failed to initialize Firebase Messaging for token cleanup.");
            return;
        }

        $tokens_to_check = [];
        $user_id_map = [];
        foreach ($users_with_tokens as $user) {
            $token = get_user_meta($user->ID, 'q_push_token', true);
            if (!empty($token)) {
                $tokens_to_check[] = $token;
                $user_id_map[$token] = $user->ID;
            }
        }

        if (empty($tokens_to_check)) {
            error_log("No valid FCM tokens found to check during cleanup.");
            return;
        }

        // Check token validity using Firebase Messaging (batch check is more efficient)
        try {
            $response = $messaging->validateRegistrationTokens($tokens_to_check);

            // Process invalid tokens
            if (!empty($response['invalid'])) {
                error_log("Found " . count($response['invalid']) . " invalid FCM tokens during cleanup.");
                foreach ($response['invalid'] as $invalid_token) {
                    $user_id = $user_id_map[$invalid_token];
                    self::invalidate_token($user_id); // Use the class method
                }
            }

            // Process unknown tokens (might be expired or invalid)
            if (!empty($response['unknown'])) {
                 error_log("Found " . count($response['unknown']) . " unknown FCM tokens during cleanup.");
                 foreach ($response['unknown'] as $unknown_token) {
                     $user_id = $user_id_map[$unknown_token];
                     self::invalidate_token($user_id); // Use the class method
                 }
             }

        } catch (\Kreait\Firebase\Exception\MessagingException $e) {
            error_log("Firebase Messaging Exception during token cleanup: " . $e->getMessage());
        } catch (\Exception $e) {
            error_log("An unexpected error occurred during token cleanup: " . $e->getMessage());
        }
    }
}