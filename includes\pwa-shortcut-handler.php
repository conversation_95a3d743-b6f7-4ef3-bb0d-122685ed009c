<?php
/**
 * PWA Shortcut Handler PHP
 * 
 * This file is now deprecated. Use pwa-shortcut-handler.js instead.
 * This file is kept for backward compatibility.
 */

defined('ABSPATH') or die('No script kiddies please!');

// Include the JavaScript version instead
wp_enqueue_script(
    'q-pwa-shortcut-handler',
    Q_PLUGIN_URL . 'includes/js/pwa-shortcut-handler.js',
    array(),
    '1.0.0',
    true
);

// Add filter to remove pwa_shortcut parameter from URLs
add_filter('script_loader_src', 'q_pwa_clean_shortcut_urls', 15, 1);
add_filter('style_loader_src', 'q_pwa_clean_shortcut_urls', 15, 1);
add_filter('the_permalink', 'q_pwa_clean_shortcut_urls', 15, 1);
add_filter('page_link', 'q_pwa_clean_shortcut_urls', 15, 1);
add_filter('post_link', 'q_pwa_clean_shortcut_urls', 15, 1);
add_filter('term_link', 'q_pwa_clean_shortcut_urls', 15, 1);

function q_pwa_clean_shortcut_urls($url) {
    if (is_string($url)) {
        return remove_query_arg('pwa_shortcut', $url);
    }
    return $url;
}