/**
 * PWA Analytics Dashboard JavaScript
 */
(function($) {
    'use strict';
    
    // Initialize analytics dashboard
    function initAnalyticsDashboard() {
        // Handle date range picker if available
        if ($.fn.daterangepicker) {
            $('.q-pwa-date-range-picker').daterangepicker({
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                },
                startDate: moment().subtract(29, 'days'),
                endDate: moment(),
                locale: {
                    format: 'YYYY-MM-DD'
                }
            });
        }
        
        // Export to CSV functionality
        $('#exportAnalyticsCSV').on('click', function() {
            const table = document.querySelector('.q-pwa-analytics-table-container table');
            if (!table) return;
            
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // Add headers
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.trim());
            });
            csvContent += headers.join(',') + "\n";
            
            // Add rows
            table.querySelectorAll('tbody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    // Escape commas in the content
                    let content = td.textContent.trim();
                    if (content.includes(',')) {
                        content = `"${content}"`;
                    }
                    row.push(content);
                });
                csvContent += row.join(',') + "\n";
            });
            
            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "pwa_analytics_export.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
        
        // Refresh data
        $('#refreshAnalytics').on('click', function() {
            const button = $(this);
            button.prop('disabled', true).text('Refreshing...');
            
            // Add spinner if available
            if (button.find('.spinner').length === 0) {
                button.prepend('<span class="spinner is-active" style="float:none;margin-right:5px;"></span>');
            }
            
            // Reload the page after a short delay
            setTimeout(function() {
                location.reload();
            }, 500);
        });
        
        // Initialize tooltips for chart bars
        $('.q-pwa-chart-bar').each(function() {
            const bar = $(this);
            const tooltip = bar.attr('title');
            
            if (tooltip) {
                bar.on('mouseenter', function() {
                    // Create tooltip element if it doesn't exist
                    if ($('#q-pwa-chart-tooltip').length === 0) {
                        $('body').append('<div id="q-pwa-chart-tooltip"></div>');
                    }
                    
                    const tooltipEl = $('#q-pwa-chart-tooltip');
                    tooltipEl.html(tooltip);
                    
                    // Position tooltip
                    const barOffset = bar.offset();
                    tooltipEl.css({
                        top: barOffset.top - tooltipEl.outerHeight() - 10,
                        left: barOffset.left + (bar.outerWidth() / 2) - (tooltipEl.outerWidth() / 2)
                    }).show();
                });
                
                bar.on('mouseleave', function() {
                    $('#q-pwa-chart-tooltip').hide();
                });
            }
        });
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        initAnalyticsDashboard();
    });
    
    // Add tooltip styles
    $('head').append(`
        <style>
            #q-pwa-chart-tooltip {
                position: absolute;
                background: rgba(0,0,0,0.8);
                color: #fff;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 12px;
                z-index: 1000;
                pointer-events: none;
                display: none;
            }
            
            #q-pwa-chart-tooltip:after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 50%;
                margin-left: -5px;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid rgba(0,0,0,0.8);
            }
        </style>
    `);
    
})(jQuery);