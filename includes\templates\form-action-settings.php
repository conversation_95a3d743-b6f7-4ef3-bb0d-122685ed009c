<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.'); // Prevent direct access
}

// Initialize variables with default values from options
$title = isset($options['title']) ? sanitize_text_field($options['title']) : '';
$message = isset($options['message']) ? sanitize_textarea_field($options['message']) : '';
$subscribers = isset($options['subscribers']) ? $options['subscribers'] : array();
$notification_icon = isset($options['notification_icon']) ? esc_url($options['notification_icon']) : '';
$notification_url = isset($options['notification_url']) ? $options['notification_url'] : '';
$notification_image = isset($options['notification_image']) ? esc_url($options['notification_image']) : '';

// Add media uploader script
add_action('admin_enqueue_scripts', function() {
    wp_enqueue_media();
});

// Add inline script for media uploader
?>
<script type="text/javascript">
jQuery(document).ready(function($) {
    $('.q-media-upload').on('click', function(e) {
        e.preventDefault();
        var button = $(this);
        var targetInput = button.data('target');
        
        var mediaUploader = wp.media({
            title: '<?php esc_html_e('Select Image', 'formidable-firebase-push'); ?>',
            button: {
                text: '<?php esc_html_e('Use this image', 'formidable-firebase-push'); ?>'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $('input[name="' + targetInput + '"]').val(attachment.url);
        });

        mediaUploader.open();
    });
});
</script>
<?php
$trigger_delay = isset($options['trigger_delay']) ? intval($options['trigger_delay']) : 0;

// Example of error handling when retrieving users
try {
    $all_subscribers = get_users(array(
        'meta_key' => 'q_push_token',
    ));
} catch (Exception $e) {
    error_log("Error retrieving users: " . $e->getMessage());
    // Handle error, e.g., show a message to the user
}

// Group users by role for better organization
$users_by_role = array();
error_log('All Subscribers before loop: ' . print_r($all_subscribers, true));
foreach ($all_subscribers as $user) {
    if (isset($user->roles) && is_array($user->roles)) {
        foreach ($user->roles as $role) {
            if (!isset($users_by_role[$role])) {
                $users_by_role[$role] = array(); // Initialize role array if not set
            }
            $users_by_role[$role][] = $user; // Add user to the corresponding role
        }
    }
}
error_log('Users by role: ' . print_r($users_by_role, true));
?>

<div class="frm_form_field">
    <div class="frm_section_heading">
        <h2><?php esc_html_e('Push Notification Settings', 'formidable-firebase-push'); ?></h2>
    </div>

    <table class="form-table">
        <tr>
            <th>
                <label><?php esc_html_e('Notification Title', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter the title for your push notification. You can use [field_id] shortcodes.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('title')); ?>"
                    value="<?php echo esc_attr($title); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter notification title', 'formidable-firebase-push'); ?>" />
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Notification Message', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter the message for your push notification. You can use [field_id] shortcodes.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <textarea name="<?php echo esc_attr($this->get_field_name('message')); ?>" class="large-text" rows="5"
                    placeholder="<?php esc_attr_e('Enter notification message', 'formidable-firebase-push'); ?>"><?php echo esc_textarea($message); ?></textarea>
                <p class="description">
                    <?php esc_html_e('Use Formidable field shortcodes like [7] or [field_key] to insert field values. WordPress shortcodes are also supported.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Notification Icon', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter an icon URL. This will be shown as the notification icon.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('notification_icon')); ?>"
                    value="<?php echo esc_attr($notification_icon); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter icon URL', 'formidable-firebase-push'); ?>" />
                <a href="#" class="button frm-button-secondary q-media-upload" data-target="<?php echo esc_attr($this->get_field_name('notification_icon')); ?>">
                    <?php esc_html_e('Select Icon', 'formidable-firebase-push'); ?>
                </a>
                <p class="description">
                    <?php esc_html_e('Enter a direct URL to an icon image (recommended size: 192x192px). Leave empty for no icon.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>
        
        <tr>
            <th>
                <label><?php esc_html_e('Notification Image', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter an image URL or use field shortcodes. The image will be shown in the notification.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('notification_image')); ?>"
                    value="<?php echo esc_attr($notification_image); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter image URL or shortcode', 'formidable-firebase-push'); ?>" />
                <a href="#" class="button frm-button-secondary q-media-upload" data-target="<?php echo esc_attr($this->get_field_name('notification_image')); ?>">
                    <?php esc_html_e('Select Image', 'formidable-firebase-push'); ?>
                </a>
                <p class="description">
                    <?php esc_html_e('Enter a direct image URL or use Formidable field shortcodes like [7] to use uploaded images.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>
        
        <tr>
            <th>
                <label><?php esc_html_e('Notification URL', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enter a URL to redirect when the notification is clicked. You can use Formidable field shortcodes like [field_id]. Leave empty to use the site home page.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('notification_url')); ?>"
                    value="<?php echo esc_attr($notification_url); ?>" class="large-text"
                    placeholder="<?php esc_attr_e('Enter URL or shortcode (e.g. [field_id])', 'formidable-firebase-push'); ?>" />
                <p class="description">
                    <?php esc_html_e('Enter a URL to redirect users when they click the notification. You can use Formidable field shortcodes like [field_id]. If left empty, the site home page will be used.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>
        
        <tr>
            <th>
                <label><?php esc_html_e('Rich Media Type', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Select the type of rich media to include in the notification.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <select name="<?php echo esc_attr($this->get_field_name('rich_media_type')); ?>" id="q-rich-media-type">
                    <option value="" <?php selected(isset($options['rich_media_type']) ? $options['rich_media_type'] : '', ''); ?>><?php esc_html_e('Standard Image', 'formidable-firebase-push'); ?></option>
                    <option value="big-picture" <?php selected(isset($options['rich_media_type']) ? $options['rich_media_type'] : '', 'big-picture'); ?>><?php esc_html_e('Big Picture', 'formidable-firebase-push'); ?></option>
                    <option value="video" <?php selected(isset($options['rich_media_type']) ? $options['rich_media_type'] : '', 'video'); ?>><?php esc_html_e('Video Thumbnail', 'formidable-firebase-push'); ?></option>
                    <option value="gif" <?php selected(isset($options['rich_media_type']) ? $options['rich_media_type'] : '', 'gif'); ?>><?php esc_html_e('GIF Animation', 'formidable-firebase-push'); ?></option>
                    <option value="audio" <?php selected(isset($options['rich_media_type']) ? $options['rich_media_type'] : '', 'audio'); ?>><?php esc_html_e('Audio', 'formidable-firebase-push'); ?></option>
                </select>
                <p class="description">
                    <?php esc_html_e('Select the type of rich media to include in your notification.', 'formidable-firebase-push'); ?>
                </p>
                
                <div id="q-rich-media-fields" style="margin-top: 10px;">
                    <!-- Big Picture fields -->
                    <div class="q-rich-media-field" id="q-big-picture-field" style="display: none;">
                        <label><?php esc_html_e('Large Image URL', 'formidable-firebase-push'); ?></label>
                        <input type="text" name="<?php echo esc_attr($this->get_field_name('large_image')); ?>"
                            value="<?php echo esc_attr(isset($options['large_image']) ? $options['large_image'] : ''); ?>" class="large-text"
                            placeholder="<?php esc_attr_e('Enter large image URL or shortcode', 'formidable-firebase-push'); ?>" />
                        <a href="#" class="button frm-button-secondary q-media-upload" data-target="<?php echo esc_attr($this->get_field_name('large_image')); ?>">
                            <?php esc_html_e('Select Image', 'formidable-firebase-push'); ?>
                        </a>
                    </div>
                    
                    <!-- Video fields -->
                    <div class="q-rich-media-field" id="q-video-field" style="display: none;">
                        <label><?php esc_html_e('Video URL', 'formidable-firebase-push'); ?></label>
                        <input type="text" name="<?php echo esc_attr($this->get_field_name('video_url')); ?>"
                            value="<?php echo esc_attr(isset($options['video_url']) ? $options['video_url'] : ''); ?>" class="large-text"
                            placeholder="<?php esc_attr_e('Enter video URL (YouTube or direct link)', 'formidable-firebase-push'); ?>" />
                    </div>
                    
                    <!-- GIF fields -->
                    <div class="q-rich-media-field" id="q-gif-field" style="display: none;">
                        <label><?php esc_html_e('GIF URL', 'formidable-firebase-push'); ?></label>
                        <input type="text" name="<?php echo esc_attr($this->get_field_name('gif_url')); ?>"
                            value="<?php echo esc_attr(isset($options['gif_url']) ? $options['gif_url'] : ''); ?>" class="large-text"
                            placeholder="<?php esc_attr_e('Enter GIF URL', 'formidable-firebase-push'); ?>" />
                        <a href="#" class="button frm-button-secondary q-media-upload" data-target="<?php echo esc_attr($this->get_field_name('gif_url')); ?>">
                            <?php esc_html_e('Select GIF', 'formidable-firebase-push'); ?>
                        </a>
                    </div>
                    
                    <!-- Audio fields -->
                    <div class="q-rich-media-field" id="q-audio-field" style="display: none;">
                        <label><?php esc_html_e('Audio URL', 'formidable-firebase-push'); ?></label>
                        <input type="text" name="<?php echo esc_attr($this->get_field_name('audio_url')); ?>"
                            value="<?php echo esc_attr(isset($options['audio_url']) ? $options['audio_url'] : ''); ?>" class="large-text"
                            placeholder="<?php esc_attr_e('Enter audio URL', 'formidable-firebase-push'); ?>" />
                        <label style="margin-top: 5px; display: block;"><?php esc_html_e('Audio Title', 'formidable-firebase-push'); ?></label>
                        <input type="text" name="<?php echo esc_attr($this->get_field_name('audio_title')); ?>"
                            value="<?php echo esc_attr(isset($options['audio_title']) ? $options['audio_title'] : ''); ?>" class="large-text"
                            placeholder="<?php esc_attr_e('Enter audio title', 'formidable-firebase-push'); ?>" />
                    </div>
                </div>
                
                <script>
                jQuery(document).ready(function($) {
                    // Show/hide rich media fields based on selection
                    function updateRichMediaFields() {
                        var selectedType = $('#q-rich-media-type').val();
                        $('.q-rich-media-field').hide();
                        
                        if (selectedType === 'big-picture') {
                            $('#q-big-picture-field').show();
                        } else if (selectedType === 'video') {
                            $('#q-video-field').show();
                        } else if (selectedType === 'gif') {
                            $('#q-gif-field').show();
                        } else if (selectedType === 'audio') {
                            $('#q-audio-field').show();
                        }
                    }
                    
                    // Initial update
                    updateRichMediaFields();
                    
                    // Update on change
                    $('#q-rich-media-type').on('change', updateRichMediaFields);
                });
                </script>
            </td>
        </tr>
        
        <tr>
            <th>
                <label><?php esc_html_e('Emoji in Title/Message', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('You can include emojis in your notification title and message.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <div class="q-emoji-picker">
                    <button type="button" class="button q-emoji-button" id="q-emoji-picker-btn">
                        <?php esc_html_e('Insert Emoji', 'formidable-firebase-push'); ?> 😀
                    </button>
                    <div class="q-emoji-container" style="display:none;">
                        <div class="q-emoji-list">
                            <?php
                            $common_emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '🥲', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😌', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️', '😮', '😯', '😲', '😳', '🥺', '😦', '😧', '😨', '😰', '😥', '😢', '😭', '😱', '😖', '😣', '😞', '😓', '😩', '😫', '🥱', '😤', '😡', '😠', '🤬', '😈', '👿', '💀', '☠️', '💩', '🤡', '👹', '👺', '👻', '👽', '👾', '🤖', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '🙈', '🙉', '🙊', '💋', '💌', '💘', '💝', '💖', '💗', '💓', '💞', '💕', '💟', '❣️', '💔', '❤️', '🧡', '💛', '💚', '💙', '💜', '🤎', '🖤', '🤍', '💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💣', '💬', '👁️‍🗨️', '🗨️', '🗯️', '💭', '💤', '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '✊', '👊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃', '🧠', '🫀', '🫁', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋'];
                            foreach ($common_emojis as $emoji) {
                                echo '<span class="q-emoji" data-emoji="' . esc_attr($emoji) . '">' . $emoji . '</span>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <p class="description">
                    <?php esc_html_e('Click the button above to insert emojis into your notification title or message.', 'formidable-firebase-push'); ?>
                </p>
                <style>
                    .q-emoji-container {
                        max-width: 400px;
                        max-height: 200px;
                        overflow-y: auto;
                        background: white;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        padding: 10px;
                        margin-top: 5px;
                        position: absolute;
                        z-index: 100;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                    }
                    .q-emoji-list {
                        display: flex;
                        flex-wrap: wrap;
                    }
                    .q-emoji {
                        cursor: pointer;
                        font-size: 20px;
                        width: 30px;
                        height: 30px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: transform 0.1s;
                    }
                    .q-emoji:hover {
                        transform: scale(1.2);
                        background: #f0f0f0;
                        border-radius: 4px;
                    }
                </style>
                <script>
                jQuery(document).ready(function($) {
                    $('#q-emoji-picker-btn').on('click', function() {
                        $('.q-emoji-container').toggle();
                    });
                    
                    $('.q-emoji').on('click', function() {
                        const emoji = $(this).data('emoji');
                        const activeElement = document.activeElement;
                        
                        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                            const start = activeElement.selectionStart;
                            const end = activeElement.selectionEnd;
                            const value = activeElement.value;
                            
                            activeElement.value = value.substring(0, start) + emoji + value.substring(end);
                            activeElement.selectionStart = activeElement.selectionEnd = start + emoji.length;
                            activeElement.focus();
                        } else {
                            // If no input is focused, copy to clipboard
                            navigator.clipboard.writeText(emoji).then(() => {
                                alert('Emoji copied to clipboard!');
                            });
                        }
                        
                        $('.q-emoji-container').hide();
                    });
                    
                    // Close emoji picker when clicking outside
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('.q-emoji-picker').length) {
                            $('.q-emoji-container').hide();
                        }
                    });
                });
                </script>
            </td>
        </tr>
        
        <!-- <tr>
            <th>
                <label><?php esc_html_e('Action Buttons', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Add action buttons to your notification. You can use manual values or Formidable field IDs.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <div class="q-action-buttons">
                    <h4><?php esc_html_e('Button 1', 'formidable-firebase-push'); ?></h4>
                    <div class="q-action-button-row">
                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;"><?php esc_html_e('Button Text', 'formidable-firebase-push'); ?></label>
                            <input type="text" name="<?php echo esc_attr($this->get_field_name('action_button_1_text')); ?>"
                                value="<?php echo esc_attr(isset($options['action_button_1_text']) ? $options['action_button_1_text'] : ''); ?>" 
                                class="regular-text"
                                placeholder="<?php esc_attr_e('Enter button text or use [field_id]', 'formidable-firebase-push'); ?>" />
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;"><?php esc_html_e('Button URL', 'formidable-firebase-push'); ?></label>
                            <input type="text" name="<?php echo esc_attr($this->get_field_name('action_button_1_url')); ?>"
                                value="<?php echo esc_attr(isset($options['action_button_1_url']) ? $options['action_button_1_url'] : ''); ?>" 
                                class="regular-text"
                                placeholder="<?php esc_attr_e('Enter URL or use [field_id]', 'formidable-firebase-push'); ?>" />
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 20px;"><?php esc_html_e('Button 2', 'formidable-firebase-push'); ?></h4>
                    <div class="q-action-button-row">
                        <div style="margin-bottom: 10px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;"><?php esc_html_e('Button Text', 'formidable-firebase-push'); ?></label>
                            <input type="text" name="<?php echo esc_attr($this->get_field_name('action_button_2_text')); ?>"
                                value="<?php echo esc_attr(isset($options['action_button_2_text']) ? $options['action_button_2_text'] : ''); ?>" 
                                class="regular-text"
                                placeholder="<?php esc_attr_e('Enter button text or use [field_id]', 'formidable-firebase-push'); ?>" />
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;"><?php esc_html_e('Button URL', 'formidable-firebase-push'); ?></label>
                            <input type="text" name="<?php echo esc_attr($this->get_field_name('action_button_2_url')); ?>"
                                value="<?php echo esc_attr(isset($options['action_button_2_url']) ? $options['action_button_2_url'] : ''); ?>" 
                                class="regular-text"
                                placeholder="<?php esc_attr_e('Enter URL or use [field_id]', 'formidable-firebase-push'); ?>" />
                        </div>
                    </div>
                </div>
                <p class="description">
                    <?php esc_html_e('Add up to two action buttons to your notification. You can enter manual values or use Formidable field shortcodes like [field_id].', 'formidable-firebase-push'); ?>
                    <?php esc_html_e('Note: Action buttons are not supported on all platforms.', 'formidable-firebase-push'); ?>
                </p>
                
                <style>
                .q-action-buttons {
                    background: #f9f9f9;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #ddd;
                }
                .q-action-buttons h4 {
                    margin: 0 0 10px 0;
                    color: #333;
                    font-size: 14px;
                }
                .q-action-button-row {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                </style>
            </td>
        </tr> -->

        <tr>
            <th>
                <label><?php esc_html_e('Subscriber Emails/Shortcodes', 'formidable-firebase-push'); ?></label>
            </th>
            <td>
                <input type="text" name="<?php echo esc_attr($this->get_field_name('subscriber_email')); ?>[]"
                    value="<?php echo esc_attr(isset($options['subscriber_email']) ? implode(',', (array) $options['subscriber_email']) : ''); ?>"
                    class="large-text"
                    placeholder="<?php esc_attr_e('Enter comma-separated emails or shortcodes', 'formidable-firebase-push'); ?>" />
                <p class="description">
                    <?php esc_html_e('Enter the email address of the subscriber to send the notification to. You can use [field_id] shortcodes. To send to multiple emails, separate them with commas.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>

        <tr>
            <th>
                <label><?php esc_html_e('Retrigger Notifications', 'formidable-firebase-push'); ?></label>
                <span class="frm_help frm_icon_font frm_tooltip_icon"
                    title="<?php esc_attr_e('Enable to automatically resend notifications if they have not been clicked.', 'formidable-firebase-push'); ?>"></span>
            </th>
            <td>
                <label>
                    <input type="checkbox" name="<?php echo esc_attr($this->get_field_name('enable_retrigger')); ?>"
                        value="1" <?php checked(isset($options['enable_retrigger']) ? $options['enable_retrigger'] : 0); ?> />
                    <?php esc_html_e('Enable notification retriggering', 'formidable-firebase-push'); ?>
                </label>
                <div style="margin-top: 10px;">
                    <label>
                        <?php esc_html_e('Retrigger after', 'formidable-firebase-push'); ?>
                        <input type="number" name="<?php echo esc_attr($this->get_field_name('retrigger_delay')); ?>"
                            value="<?php echo esc_attr(isset($options['retrigger_delay']) ? intval($options['retrigger_delay']) : 24); ?>"
                            min="1" max="72" style="width: 60px;" />
                        <?php esc_html_e('hours if not clicked', 'formidable-firebase-push'); ?>
                    </label>
                </div>
                <div style="margin-top: 10px;">
                    <label>
                        <?php esc_html_e('Maximum retrigger attempts', 'formidable-firebase-push'); ?>
                        <input type="number"
                            name="<?php echo esc_attr($this->get_field_name('retrigger_max_attempts')); ?>"
                            value="<?php echo esc_attr(isset($options['retrigger_max_attempts']) ? intval($options['retrigger_max_attempts']) : 3); ?>"
                            min="1" max="10" style="width: 60px;" />
                    </label>
                </div>
                <p class="description">
                    <?php esc_html_e('If enabled, notifications will be automatically resent if the user has not clicked them within the specified time period.', 'formidable-firebase-push'); ?>
                </p>
            </td>
        </tr>
    </table>
</div>