<?php
/**
 * Automated test script for PWA Analytics shortcodes
 * 
 * This script tests the shortcode functionality programmatically
 * Run via WP-CLI: wp eval-file test-shortcodes.php
 * Or include in a WordPress page/plugin for testing
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // If running standalone, load WordPress
    require_once('wp-config.php');
    require_once('wp-load.php');
}

class PWA_Analytics_Shortcode_Tests {
    
    private $test_results = [];
    private $passed = 0;
    private $failed = 0;
    
    public function run_all_tests() {
        echo "=== PWA Analytics Shortcode Tests ===\n\n";
        
        $this->test_holistic_stats_shortcode();
        $this->test_user_specific_stats_shortcode();
        $this->test_shortcode_parameters();
        $this->test_database_functionality();
        $this->test_error_handling();
        
        $this->print_summary();
    }
    
    private function test_holistic_stats_shortcode() {
        echo "Testing Holistic Stats Shortcode...\n";
        
        // Test basic shortcode
        $result = do_shortcode('[holistic_stats]');
        $this->assert_contains($result, 'q-holistic-stats', 'Basic holistic stats shortcode renders');
        
        // Test with parameters
        $result = do_shortcode('[holistic_stats period="week" format="dashboard"]');
        $this->assert_contains($result, 'q-format-dashboard', 'Dashboard format parameter works');
        
        // Test table format
        $result = do_shortcode('[holistic_stats format="table"]');
        $this->assert_contains($result, 'q-stats-table', 'Table format parameter works');
        
        // Test simple format
        $result = do_shortcode('[holistic_stats format="simple"]');
        $this->assert_contains($result, 'q-stats-simple', 'Simple format parameter works');
        
        echo "\n";
    }
    
    private function test_user_specific_stats_shortcode() {
        echo "Testing User-Specific Stats Shortcode...\n";
        
        // Test with current user
        $result = do_shortcode('[user_specific_stats current_user="true"]');
        if (is_user_logged_in()) {
            $this->assert_contains($result, 'q-user-stats', 'Current user stats shortcode renders');
        } else {
            $this->assert_contains($result, 'Valid user ID required', 'Requires login when current_user=true');
        }
        
        // Test with specific user ID
        $result = do_shortcode('[user_specific_stats user_id="1"]');
        $this->assert_contains($result, 'q-user-stats', 'Specific user ID shortcode renders');
        
        // Test summary format
        $result = do_shortcode('[user_specific_stats user_id="1" format="summary"]');
        $this->assert_contains($result, 'q-user-summary', 'Summary format parameter works');
        
        // Test invalid user ID
        $result = do_shortcode('[user_specific_stats user_id="invalid"]');
        $this->assert_contains($result, 'Valid user ID required', 'Invalid user ID handled correctly');
        
        echo "\n";
    }
    
    private function test_shortcode_parameters() {
        echo "Testing Shortcode Parameters...\n";
        
        // Test period parameters
        $periods = ['today', 'yesterday', 'week', 'month', 'all'];
        foreach ($periods as $period) {
            $result = do_shortcode("[holistic_stats period=\"$period\"]");
            $this->assert_not_empty($result, "Period '$period' parameter works");
        }
        
        // Test show parameters for holistic stats
        $show_options = ['all', 'events', 'users', 'devices', 'engagement'];
        foreach ($show_options as $show) {
            $result = do_shortcode("[holistic_stats show=\"$show\"]");
            $this->assert_not_empty($result, "Show '$show' parameter works");
        }
        
        // Test style parameters
        $styles = ['default', 'compact', 'detailed'];
        foreach ($styles as $style) {
            $result = do_shortcode("[holistic_stats style=\"$style\"]");
            $this->assert_contains($result, "q-style-$style", "Style '$style' parameter works");
        }
        
        echo "\n";
    }
    
    private function test_database_functionality() {
        echo "Testing Database Functionality...\n";
        
        global $wpdb;
        $pwa_table = $wpdb->prefix . 'q_pwa_analytics';
        $notification_table = $wpdb->prefix . 'q_notification_analytics';
        
        // Check if tables exist
        $pwa_exists = $wpdb->get_var("SHOW TABLES LIKE '$pwa_table'") == $pwa_table;
        $this->assert_true($pwa_exists, 'PWA analytics table exists');
        
        $notification_exists = $wpdb->get_var("SHOW TABLES LIKE '$notification_table'") == $notification_table;
        $this->assert_true($notification_exists, 'Notification analytics table exists');
        
        if ($pwa_exists) {
            // Check table schema
            $columns = $wpdb->get_col("DESCRIBE $pwa_table");
            $required_columns = ['event_name', 'user_id', 'device_type', 'os', 'browser', 'session_id', 'timestamp'];
            
            foreach ($required_columns as $column) {
                $this->assert_true(in_array($column, $columns), "Column '$column' exists in PWA analytics table");
            }
            
            // Test data insertion (if we have the function available)
            if (function_exists('q_pwa_track_event')) {
                // This would require setting up proper nonce and POST data
                // For now, just check that the function exists
                $this->assert_true(true, 'Analytics tracking function exists');
            }
        }
        
        echo "\n";
    }
    
    private function test_error_handling() {
        echo "Testing Error Handling...\n";
        
        // Test shortcode with missing required parameters
        $result = do_shortcode('[user_specific_stats]');
        $this->assert_contains($result, 'Valid user ID required', 'Missing user_id parameter handled');
        
        // Test with invalid format
        $result = do_shortcode('[holistic_stats format="invalid"]');
        $this->assert_not_empty($result, 'Invalid format parameter handled gracefully');
        
        // Test with invalid period
        $result = do_shortcode('[holistic_stats period="invalid"]');
        $this->assert_not_empty($result, 'Invalid period parameter handled gracefully');
        
        echo "\n";
    }
    
    private function assert_contains($haystack, $needle, $message) {
        if (strpos($haystack, $needle) !== false) {
            $this->pass($message);
        } else {
            $this->fail($message . " (Expected to contain: '$needle')");
        }
    }
    
    private function assert_not_empty($value, $message) {
        if (!empty($value)) {
            $this->pass($message);
        } else {
            $this->fail($message . " (Value was empty)");
        }
    }
    
    private function assert_true($condition, $message) {
        if ($condition) {
            $this->pass($message);
        } else {
            $this->fail($message);
        }
    }
    
    private function pass($message) {
        echo "✓ PASS: $message\n";
        $this->passed++;
        $this->test_results[] = ['status' => 'PASS', 'message' => $message];
    }
    
    private function fail($message) {
        echo "✗ FAIL: $message\n";
        $this->failed++;
        $this->test_results[] = ['status' => 'FAIL', 'message' => $message];
    }
    
    private function print_summary() {
        echo "\n=== Test Summary ===\n";
        echo "Total Tests: " . ($this->passed + $this->failed) . "\n";
        echo "Passed: $this->passed\n";
        echo "Failed: $this->failed\n";
        
        if ($this->failed > 0) {
            echo "\nFailed Tests:\n";
            foreach ($this->test_results as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "- " . $result['message'] . "\n";
                }
            }
        }
        
        echo "\nOverall Result: " . ($this->failed === 0 ? "ALL TESTS PASSED" : "SOME TESTS FAILED") . "\n";
    }
}

// Run tests if this file is executed directly
if (basename($_SERVER['PHP_SELF']) === 'test-shortcodes.php' || defined('WP_CLI')) {
    $tester = new PWA_Analytics_Shortcode_Tests();
    $tester->run_all_tests();
}

// Function to run tests programmatically
function run_pwa_analytics_tests() {
    $tester = new PWA_Analytics_Shortcode_Tests();
    $tester->run_all_tests();
}
