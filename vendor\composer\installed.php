<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '2.2.2',
        'version' => '2.2.2.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '2.2.2',
            'version' => '2.2.2.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/clock' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '7004b55fcd54737b539886244b3a3b2188181974',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/in-memory-cache' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'f8299adc8abdaf7d309e8b28e53b4307ea49ebc7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/in-memory-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'beste/json' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => 'a25b883c71f84aee3709d5635e71286af1fdc8eb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beste/json',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.13.1',
            'version' => '0.13.1.0',
            'reference' => 'fc7ed316430118cc7836bf45faff18d5dfc8de04',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.11.1',
            'version' => '6.11.1.0',
            'reference' => 'd1e91ecf8c598d073d0995afa8cd5c75c6e19e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.47.0',
            'version' => '1.47.0.0',
            'reference' => 'd6389aae7c009daceaa8da9b7942d8df6969f6d9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-core' => array(
            'pretty_version' => 'v1.63.0',
            'version' => '1.63.0.0',
            'reference' => '14461f7b53b261caeed7174f9d704d10881b02c2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/cloud-storage' => array(
            'pretty_version' => 'v1.48.1',
            'version' => '1.48.1.0',
            'reference' => '509b095c3ea44db92c9e62a94b5773563c831821',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-storage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/common-protos' => array(
            'pretty_version' => '4.12.1',
            'version' => '4.12.1.0',
            'reference' => '70c4eb1abab5484a23c17a43b0d455259f5d8c1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/common-protos',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/gax' => array(
            'pretty_version' => 'v1.36.1',
            'version' => '1.36.1.0',
            'reference' => 'afdac3bc38a3b17d70668115d7b1a97289ac4d72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/gax',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/grpc-gcp' => array(
            'pretty_version' => 'v0.4.1',
            'version' => '0.4.1.0',
            'reference' => 'e585b7721bbe806ef45b5c52ae43dfc2bff89968',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/grpc-gcp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/longrunning' => array(
            'pretty_version' => '0.4.7',
            'version' => '0.4.7.0',
            'reference' => '624cabb874c10e5ddc9034c999f724894b70a3d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/longrunning',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'google/protobuf' => array(
            'pretty_version' => 'v4.31.1',
            'version' => '4.31.1.0',
            'reference' => '2b028ce8876254e2acbeceea7d9b573faad41864',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/protobuf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grpc/grpc' => array(
            'pretty_version' => '1.57.0',
            'version' => '1.57.0.0',
            'reference' => 'b610c42022ed3a22f831439cb93802f2a4502fdf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grpc/grpc',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-php' => array(
            'pretty_version' => '7.19.0',
            'version' => '7.19.0.0',
            'reference' => 'b06a2dd84eb5e2c4042773dce55b9291c0512d6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kreait/firebase-tokens' => array(
            'pretty_version' => '5.2.1',
            'version' => '5.2.1.0',
            'reference' => 'df6f9d153f3bbe671c3247576d2a45cbd0a79620',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kreait/firebase-tokens',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '5.5.0',
            'version' => '5.5.0.0',
            'reference' => 'a835af59b030d3f2967725697cf88300f579088e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/jmespath.php' => array(
            'pretty_version' => '2.8.0',
            'version' => '2.8.0.0',
            'reference' => 'a2a865e05d5f420b50cc2f85bb78d565db12a6bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mtdowling/jmespath.php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0 || 3.0',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.8.1',
            'version' => '4.8.1.0',
            'reference' => 'fdf4dd4e2ff1813111bd0ad58d7a1ddbb5b56c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.8.1',
            ),
        ),
        'rize/uri-template' => array(
            'pretty_version' => '0.4.0',
            'version' => '0.4.0.0',
            'reference' => '56f374a9a42c7c3998f8b55b6b21b224de90c58b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rize/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
