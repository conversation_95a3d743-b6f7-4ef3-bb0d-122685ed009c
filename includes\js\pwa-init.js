/**
 * PWA Initialization Script
 * This script ensures the PWA manager is initialized properly
 */

document.addEventListener('DOMContentLoaded', () => {
    // Check if PWA settings are available
    if (typeof window.qPWASettings === 'undefined') {
        console.error('Q-PWA: PWA settings not found. Install prompt will not work.');
        return;
    }

    // Initialize PWA Manager if not already initialized
    if (typeof window.qPWAManager === 'undefined') {
        if (window.location.hostname === 'localhost') {
            console.log('Q-PWA: Initializing PWA Manager from init script');
        }
        window.qPWAManager = new QPWAManager();
    }
});