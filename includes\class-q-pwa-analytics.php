<?php
/**
 * PWA Analytics Class
 * 
 * Handles PWA analytics data collection, processing, and display
 */

if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Analytics
{
    /**
     * Initialize the analytics functionality
     */
    public static function init()
    {
        self::maybe_upgrade_schema();
        // Register AJAX endpoints
        add_action('wp_ajax_q_pwa_get_analytics', [self::class, 'ajax_get_analytics']);
        add_action('wp_ajax_q_pwa_export_analytics', [self::class, 'ajax_export_analytics']);
        
        // Register cleanup cron job
        add_action('q_pwa_analytics_cleanup', [self::class, 'cleanup_old_analytics']);
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('q_pwa_analytics_cleanup')) {
            wp_schedule_event(time(), 'daily', 'q_pwa_analytics_cleanup');
        }
        
        // Add tab to PWA settings
        add_filter('q_pwa_settings_tabs', [self::class, 'add_analytics_tab']);
    }
    
    /**
     * Add analytics tab to PWA settings
     */
    public static function add_analytics_tab($tabs)
    {
        $tabs['analytics'] = __('Analytics', 'q-pusher-q-pwa');
        return $tabs;
    }
    
    /**
     * AJAX handler for getting analytics data
     */
    public static function ajax_get_analytics()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_analytics_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        $period = isset($_POST['period']) ? sanitize_text_field($_POST['period']) : 'week';
        $event_type = isset($_POST['event_type']) ? sanitize_text_field($_POST['event_type']) : '';
        
        if (!empty($event_type)) {
            // Get chart data for specific event
            $data = q_get_pwa_analytics_chart_data($event_type, $period);
            wp_send_json_success($data);
        } else {
            // Get general analytics data
            $data = q_get_pwa_analytics($period);
            wp_send_json_success($data);
        }
    }
    
    /**
     * AJAX handler for exporting analytics data
     */
    public static function ajax_export_analytics()
    {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_analytics_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        $period = isset($_POST['period']) ? sanitize_text_field($_POST['period']) : 'all';
        
        // Get all analytics data without limit
        $data = q_get_pwa_analytics($period, 0);
        
        // Format for CSV
        $csv_data = [
            ['Event', 'Count', 'Percentage']
        ];
        
        $total = 0;
        foreach ($data as $row) {
            $total += $row['count'];
        }
        
        foreach ($data as $row) {
            $percentage = $total > 0 ? round(($row['count'] / $total) * 100, 1) . '%' : '0%';
            $csv_data[] = [$row['event_name'], $row['count'], $percentage];
        }
        
        // Send as JSON for frontend processing
        wp_send_json_success([
            'filename' => 'pwa_analytics_' . $period . '_' . date('Y-m-d') . '.csv',
            'data' => $csv_data
        ]);
    }
    
    /**
     * Clean up old analytics data based on retention settings
     */
    public static function cleanup_old_analytics()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        // Get retention period in days
        $retention_days = get_option('q_pwa_analytics_retention', 90);
        
        // Delete records older than retention period
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$table_name} WHERE timestamp < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $retention_days
        ));
    }
    
    /**
     * Create analytics tables if they don't exist
     */
    public static function create_tables()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        $charset_collate = $wpdb->get_charset_collate();
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            event_name varchar(100) NOT NULL,
            user_id bigint(20) DEFAULT 0,
            device_type varchar(20) DEFAULT NULL,
            os varchar(20) DEFAULT NULL,
            browser varchar(20) DEFAULT NULL,
            app_version varchar(20) DEFAULT NULL,
            session_id varchar(255) DEFAULT NULL,
            session_start datetime DEFAULT NULL,
            session_end datetime DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            page_url text DEFAULT NULL,
            event_data longtext,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY event_name (event_name),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY timestamp (timestamp)
        ) {$charset_collate};";
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Get user-specific analytics
     */
    public static function get_user_analytics($user_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        
        // Get device count
        $devices = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT device_type)
             FROM {$table_name}
             WHERE user_id = %s",
            $user_id
        ));
        
        // Get online status (last event within 5 minutes)
        $last_online = $wpdb->get_var($wpdb->prepare(
            "SELECT timestamp
             FROM {$table_name}
             WHERE user_id = %s
             ORDER BY timestamp DESC
             LIMIT 1",
            $user_id
        ));
        
        // Determine online status: true if last event within 5 minutes
        if ($last_online) {
            $last_online_ts = strtotime($last_online);
            $online_status = ($last_online_ts >= (time() - 300)) ? 'Online' : 'Offline';
        } else {
            $online_status = 'Offline';
        }
        
        // Get average session duration
        $avg_duration = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(TIMESTAMPDIFF(SECOND, session_start, session_end))
             FROM {$table_name}
             WHERE user_id = %s AND event_name = 'session_end'",
            $user_id
        ));
        
        // Get most visited page
        $most_visited = $wpdb->get_var($wpdb->prepare(
            "SELECT event_data->>'$.url' as url
             FROM {$table_name}
             WHERE user_id = %s AND event_name = 'pageview'
             GROUP BY url
             ORDER BY COUNT(*) DESC
             LIMIT 1",
            $user_id
        ));
        
        // Get push notifications enabled status
        $push_enabled = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*)
             FROM {$table_name}
             WHERE user_id = %s AND event_name = 'push_subscribed'",
            $user_id
        )) > 0;
        
        // Get list of devices
        $device_list = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT device_type
             FROM {$table_name}
             WHERE user_id = %s",
            $user_id
        ));
        
        // Get last known os, browser, app_version
        $os = $wpdb->get_var($wpdb->prepare("SELECT os FROM {$table_name} WHERE user_id = %s AND os IS NOT NULL AND os != '' ORDER BY timestamp DESC LIMIT 1", $user_id));
        $browser = $wpdb->get_var($wpdb->prepare("SELECT browser FROM {$table_name} WHERE user_id = %s AND browser IS NOT NULL AND browser != '' ORDER BY timestamp DESC LIMIT 1", $user_id));
        $app_version = $wpdb->get_var($wpdb->prepare("SELECT app_version FROM {$table_name} WHERE user_id = %s AND app_version IS NOT NULL AND app_version != '' ORDER BY timestamp DESC LIMIT 1", $user_id));
        
        return [
            'devices' => $devices,
            'online_status' => $online_status,
            'last_online' => $last_online,
            'avg_session_duration' => $avg_duration,
            'most_visited' => $most_visited,
            'push_enabled' => $push_enabled,
            'device_list' => $device_list,
            'os' => $os,
            'browser' => $browser,
            'app_version' => $app_version
        ];
    }
    
    /**
     * Handle user-specific analytics shortcode
     */
    public static function user_analytics_shortcode($atts) {
        $atts = shortcode_atts([
            'user_id' => '',
            'event' => ''
        ], $atts);

        if (empty($atts['user_id'])) {
            return 'User ID required';
        }

        $data = self::get_user_analytics($atts['user_id']);

        switch ($atts['event']) {
            case 'connection_online':
            case 'online':
                return $data['online_status'];
            case 'most_visited':
                return $data['most_visited'] ? esc_html($data['most_visited']) : 'N/A';
            case 'avg_session_duration':
                return $data['avg_session_duration'] ? gmdate('H:i:s', $data['avg_session_duration']) : 'N/A';
            case 'last_online':
                return $data['last_online'] ? esc_html($data['last_online']) : 'N/A';
            case 'devices':
                return intval($data['devices']);
            case 'push_enabled':
                return $data['push_enabled'] ? 'Yes' : 'No';
            case 'device_list':
                return !empty($data['device_list']) ? esc_html(implode(', ', $data['device_list'])) : 'N/A';
            case 'os':
                return $data['os'] ? esc_html($data['os']) : 'N/A';
            case 'browser':
                return $data['browser'] ? esc_html($data['browser']) : 'N/A';
            case 'app_version':
                return $data['app_version'] ? esc_html($data['app_version']) : 'N/A';
            case 'session_histogram':
                $hist = self::get_session_length_histogram($atts['user_id']);
                return $hist ? esc_html(json_encode($hist)) : 'N/A';
            default:
                return 'Unsupported event';
        }
    }

    /**
     * Verify that the analytics table matches the expected schema. If it is missing
     * critical columns, silently run the full create_tables() routine which
     * leverages dbDelta() to perform an in-place upgrade (adds columns without
     * dropping data).
     */
    private static function maybe_upgrade_schema()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        // Check whether the table exists – early exit if it does not; create
        // it from scratch in that case.
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) !== $table_name) {
            self::create_tables();
            return;
        }

        // Fetch current columns.
        $columns = $wpdb->get_col("DESCRIBE {$table_name}");

        // If any of the expected columns are missing, run dbDelta to add them.
        $expected_columns = [
            'device_type',
            'session_id',
            'session_start',
            'session_end',
            'ip_address',
            'user_agent',
            'page_url',
            'event_data',
            'browser',
            'os'
        ];

        foreach ($expected_columns as $col) {
            if (!in_array($col, $columns, true)) {
                self::create_tables();
                break;
            }
        }
    }

    // Add new queries for advanced stats
    public static function get_session_length_histogram($user_id = null) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        $where = $user_id ? $wpdb->prepare('WHERE user_id = %s', $user_id) : '';
        $bins = [0, 60, 300, 900, 3600];
        $labels = ['0-1 min', '1-5 min', '5-15 min', '15+ min'];
        $hist = array_fill_keys($labels, 0);
        $rows = $wpdb->get_results("SELECT TIMESTAMPDIFF(SECOND, session_start, session_end) as duration FROM {$table_name} WHERE event_name = 'session_end' " . ($user_id ? $where : ''), ARRAY_A);
        foreach ($rows as $row) {
            $d = intval($row['duration']);
            if ($d < 60) $hist['0-1 min']++;
            elseif ($d < 300) $hist['1-5 min']++;
            elseif ($d < 900) $hist['5-15 min']++;
            else $hist['15+ min']++;
        }
        return $hist;
    }
    public static function get_top_browsers($limit = 5) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        return $wpdb->get_results("SELECT browser, COUNT(*) as count FROM {$table_name} WHERE browser IS NOT NULL AND browser != '' GROUP BY browser ORDER BY count DESC LIMIT {$limit}", ARRAY_A);
    }
    public static function get_top_os($limit = 5) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        return $wpdb->get_results("SELECT os, COUNT(*) as count FROM {$table_name} WHERE os IS NOT NULL AND os != '' GROUP BY os ORDER BY count DESC LIMIT {$limit}", ARRAY_A);
    }
    public static function get_notification_engagement() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        $sent = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE event_name = 'notification_sent'");
        $clicked = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE event_name = 'notification_clicked'");
        $dismissed = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE event_name = 'notification_dismissed'");
        return [ 'sent' => $sent, 'clicked' => $clicked, 'dismissed' => $dismissed ];
    }
    public static function get_retention_cohorts() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';
        // Simple cohort: users who returned after 1, 7, 30 days
        $now = current_time('mysql');
        $users = $wpdb->get_col("SELECT DISTINCT user_id FROM {$table_name}");
        $cohorts = ['1d' => 0, '7d' => 0, '30d' => 0];
        foreach ($users as $user_id) {
            $first = $wpdb->get_var($wpdb->prepare("SELECT MIN(timestamp) FROM {$table_name} WHERE user_id = %s", $user_id));
            if (!$first) continue;
            $after_1d = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM {$table_name} WHERE user_id = %s AND timestamp > DATE_ADD(%s, INTERVAL 1 DAY)", $user_id, $first));
            $after_7d = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM {$table_name} WHERE user_id = %s AND timestamp > DATE_ADD(%s, INTERVAL 7 DAY)", $user_id, $first));
            $after_30d = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM {$table_name} WHERE user_id = %s AND timestamp > DATE_ADD(%s, INTERVAL 30 DAY)", $user_id, $first));
            if ($after_1d) $cohorts['1d']++;
            if ($after_7d) $cohorts['7d']++;
            if ($after_30d) $cohorts['30d']++;
        }
        return $cohorts;
    }
}

// Initialize analytics on plugin load
add_action('plugins_loaded', ['Q_PWA_Analytics', 'init']);

// Create tables on plugin activation
register_activation_hook(Q_PLUGIN_DIR . 'q-pusher.php', ['Q_PWA_Analytics', 'create_tables']);

// Register a dedicated shortcode for per-user analytics to avoid clashing with the
// generic [pwa_analytics] shortcode defined elsewhere. Example usage:
//   [pwa_user_analytics user_id="user_xyz" event="connection_online"]
add_shortcode('pwa_user_analytics', ['Q_PWA_Analytics', 'user_analytics_shortcode']);