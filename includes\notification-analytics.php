<?php
if (!defined('ABSPATH')) {
    die('You are not allowed to call this page directly.');
}

// Only declare functions if they don't already exist
if (!function_exists('q_track_notification')) {
    function q_track_notification($notification_id, $type, $data = [], $form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Ensure the table exists
        q_create_analytics_table();

        $result = $wpdb->insert(
            $table_name,
            array(
                'notification_id' => $notification_id,
                'type' => $type,
                'data' => json_encode($data),
                'form_id' => $form_id,
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s')
        );

        if ($result === false) {
            error_log('Failed to track notification: ' . $wpdb->last_error);
        }

        return $result;
    }
}

if (!function_exists('q_create_analytics_table')) {
    function q_create_analytics_table()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

            $charset_collate = $wpdb->get_charset_collate();
            $sql = "CREATE TABLE IF NOT EXISTS $table_name (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                notification_id varchar(36) NOT NULL,
                type varchar(50) NOT NULL,
                data longtext,
                form_id bigint(20),
                timestamp datetime DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY  (id),
                KEY notification_id (notification_id),
                KEY type (type),
                KEY form_id (form_id)
            ) $charset_collate;";

            dbDelta($sql);
        }
        // Also create the PWA analytics table
        q_create_pwa_analytics_table();
    }
}

if (!function_exists('q_create_pwa_analytics_table')) {
    function q_create_pwa_analytics_table()
    {
        if (class_exists('Q_PWA_Analytics')) {
            // Delegate to the canonical table-creation helper to prevent duplicate SQL definitions.
            Q_PWA_Analytics::create_tables();
        }
    }
}

if (!function_exists('q_get_total_notifications')) {
    function q_get_total_notifications($form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        $query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'sent'";
        $params = [];

        if ($form_id !== null) {
            $query .= " AND form_id = %d";
            $params[] = $form_id;
        }

        if (!empty($params)) {
            $query = $wpdb->prepare($query, ...$params);
        }

        $count = $wpdb->get_var($query);
        return intval($count ?? 0);
    }
}

if (!function_exists('q_get_engagement_rate')) {
    function q_get_engagement_rate($form_id = null)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Base queries
        $sent_query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'sent'";
        $click_query = "SELECT COUNT(DISTINCT notification_id) FROM $table_name WHERE type = 'click'";

        $params = [];

        // Add form_id condition if specified
        if ($form_id !== null) {
            $sent_query .= " AND form_id = %d";
            $click_query .= " AND form_id = %d";
            $params[] = $form_id;
            $params[] = $form_id;
        }

        // Prepare queries if we have parameters
        if (!empty($params)) {
            $sent_query = $wpdb->prepare($sent_query, ...$params);
            $click_query = $wpdb->prepare($click_query, ...$params);
        }

        $total_sent = intval($wpdb->get_var($sent_query) ?? 0);
        $total_clicks = intval($wpdb->get_var($click_query) ?? 0);

        if ($total_sent === 0) {
            return 0;
        }

        return round(($total_clicks / $total_sent) * 100, 1);
    }
}

if (!function_exists('q_get_form_analytics')) {
    function q_get_form_analytics()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // First check if the form_id column exists
        $column_exists = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = %s
            AND TABLE_NAME = %s
            AND COLUMN_NAME = 'form_id'",
            DB_NAME,
            $table_name
        ));

        if (empty($column_exists)) {
            error_log('Form analytics table needs updating. Please deactivate and reactivate the plugin.');
            return array();
        }

        // Get analytics for all forms with notifications
        $results = $wpdb->get_results("
            SELECT
                form_id,
                COUNT(DISTINCT CASE WHEN type = 'sent' THEN notification_id END) as total_notifications,
                COUNT(DISTINCT CASE WHEN type = 'click' THEN notification_id END) as total_clicks
            FROM $table_name
            WHERE form_id IS NOT NULL
            GROUP BY form_id
            ORDER BY form_id ASC
        ");

        $analytics = array();
        foreach ($results as $result) {
            $form_id = $result->form_id;
            $engagement_rate = $result->total_notifications > 0
                ? round(($result->total_clicks / $result->total_notifications) * 100, 1)
                : 0;

            $analytics[$form_id] = array(
                'total_notifications' => $result->total_notifications,
                'engagement_rate' => $engagement_rate
            );
        }

        return $analytics;
    }
}

if (!function_exists('q_track_notification_click')) {
    function q_track_notification_click()
    {
        if (!isset($_POST['notification_id'])) {
            wp_send_json_error('Missing notification ID');
            return;
        }

        $notification_id = sanitize_text_field($_POST['notification_id']);
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : null;

        q_track_notification($notification_id, 'click', [
            'timestamp' => current_time('mysql'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ], $form_id);

        wp_send_json_success();
    }
}

// Add AJAX handler for tracking notification clicks
add_action('wp_ajax_q_track_notification_click', 'q_track_notification_click');
add_action('wp_ajax_nopriv_q_track_notification_click', 'q_track_notification_click');

if (!function_exists('q_track_pwa_event')) {
    function q_track_pwa_event()
    {
        if (!isset($_POST['event']) || !isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_event_nonce')) {
            wp_send_json_error('Invalid nonce or missing event data.');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        $event_name = sanitize_text_field($_POST['event']);
        $event_data = isset($_POST['data']) ? json_decode(stripslashes($_POST['data']), true) : [];
        $timestamp = isset($_POST['timestamp']) ? sanitize_text_field($_POST['timestamp']) : current_time('mysql');

        $user_id = get_current_user_id();
        $session_id = session_id(); // Get PHP session ID
        if (empty($session_id)) {
            // Fallback for session ID if not started or available
            $session_id = isset($_COOKIE['PHPSESSID']) ? sanitize_text_field($_COOKIE['PHPSESSID']) : uniqid('pwa_session_');
        }

        $device_info = $_SERVER['HTTP_USER_AGENT'];
        $page_url = isset($_SERVER['HTTP_REFERER']) ? esc_url_raw($_SERVER['HTTP_REFERER']) : '';

        $result = $wpdb->insert(
            $table_name,
            array(
                'event_name' => $event_name,
                'user_id' => $user_id,
                'session_id' => $session_id,
                'device_info' => $device_info,
                'page_url' => $page_url,
                'event_data' => json_encode($event_data),
                'timestamp' => $timestamp
            ),
            array('%s', '%d', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            error_log('Failed to track PWA event: ' . $wpdb->last_error);
            wp_send_json_error('Failed to track event.');
        } else {
            wp_send_json_success('Event tracked successfully.');
        }
    }
}

add_action('wp_ajax_q_track_pwa_event', 'q_track_pwa_event');
add_action('wp_ajax_nopriv_q_track_pwa_event', 'q_track_pwa_event');

// Create analytics table on plugin activation
register_activation_hook(Q_PLUGIN_DIR . 'q-pusher.php', 'q_create_analytics_table');

/**
 * Find unclicked notifications that need to be retriggered
 *
 * @return array Array of notifications to retrigger
 */
if (!function_exists('q_find_unclicked_notifications')) {
    function q_find_unclicked_notifications()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Get sent notifications with retrigger enabled that haven't been clicked
        $query = "
            SELECT s.notification_id, s.data, s.form_id, s.timestamp
            FROM $table_name s
            LEFT JOIN (
                SELECT notification_id
                FROM $table_name
                WHERE type = 'click'
            ) c ON s.notification_id = c.notification_id
            WHERE s.type = 'sent'
            AND c.notification_id IS NULL
            AND (
                s.data LIKE '%\"enable_retrigger\":true%'
                OR s.data LIKE '%\"enable_retrigger\":\"true\"%'
            )
            ORDER BY s.timestamp ASC
        ";

        // error_log('Q-Pusher Debug - Running query: ' . $query);

        $results = $wpdb->get_results($query);
        $notifications_to_retrigger = [];

        foreach ($results as $result) {
            $data = json_decode($result->data, true);
            // error_log('Q-Pusher Debug - Examining notification: ' . $result->notification_id);
            // error_log('Q-Pusher Debug - Notification data: ' . print_r($data, true));

            // Skip if required data is missing
            if (!isset($data['enable_retrigger'])) {
                // error_log('Q-Pusher Debug - Missing enable_retrigger setting');
                continue;
            }

            // Handle different data formats (string vs boolean)
            $enable_retrigger = is_string($data['enable_retrigger'])
                ? ($data['enable_retrigger'] === 'true')
                : (bool) $data['enable_retrigger'];

            if (!$enable_retrigger) {
                // error_log('Q-Pusher Debug - Retriggering not enabled for this notification');
                continue;
            }

            // Set default values if missing
            if (!isset($data['retrigger_delay'])) {
                $data['retrigger_delay'] = 1; // Default to 1 hour
                // error_log('Q-Pusher Debug - Using default retrigger delay: 1 hour');
            }

            if (!isset($data['retrigger_max_attempts'])) {
                $data['retrigger_max_attempts'] = 3; // Default to 3 attempts
                // error_log('Q-Pusher Debug - Using default max attempts: 3');
            }

            if (!isset($data['retrigger_attempt'])) {
                $data['retrigger_attempt'] = 0; // Default to first attempt
                // error_log('Q-Pusher Debug - Using default current attempt: 0');
            }

            // Check if we've reached max attempts
            $current_attempt = intval($data['retrigger_attempt']);
            $max_attempts = intval($data['retrigger_max_attempts']);

            if ($current_attempt >= $max_attempts) {
                // error_log('Q-Pusher Debug - Max attempts reached: ' . $current_attempt . '/' . $max_attempts);
                continue;
            }

            // Find the timestamp of the last retrigger attempt for this notification
            $last_retrigger_time = $sent_time = strtotime($result->timestamp);

            if ($current_attempt > 0) {
                // If this notification has been retriggered before, find the most recent retrigger timestamp
                $last_retrigger_query = $wpdb->prepare(
                    "SELECT timestamp FROM $table_name
                    WHERE type = 'sent'
                    AND data LIKE %s
                    AND data LIKE %s
                    ORDER BY timestamp DESC
                    LIMIT 1",
                    '%"original_notification_id":"' . $result->notification_id . '"%',
                    '%"is_retrigger":true%'
                );

                $last_retrigger = $wpdb->get_var($last_retrigger_query);

                if ($last_retrigger) {
                    $last_retrigger_time = strtotime($last_retrigger);
                    // error_log('Q-Pusher Debug - Found previous retrigger at: ' . date('Y-m-d H:i:s', $last_retrigger_time));
                }
            }

            // Check if enough time has passed since the last retrigger (or original notification if no retriggers)
            $delay_hours = intval($data['retrigger_delay']);
            $current_time = time();
            $delay_seconds = $delay_hours * 3600;

            $time_diff = $current_time - $last_retrigger_time;
            $hours_diff = $time_diff / 3600;

            // error_log(sprintf(
            //     'Q-Pusher Debug - Time check: Last notification/retrigger=%s, Now=%s, Diff=%.2f hours, Required=%d hours',
            //     date('Y-m-d H:i:s', $last_retrigger_time),
            //     date('Y-m-d H:i:s', $current_time),
            //     $hours_diff,
            //     $delay_hours
            // ));

            if ($time_diff >= $delay_seconds) {
                // error_log('Q-Pusher Debug - Time threshold met, notification will be retriggered');

                // This notification should be retriggered
                $notifications_to_retrigger[] = [
                    'notification_id' => $result->notification_id,
                    'form_id' => $result->form_id,
                    'data' => $data,
                    'current_attempt' => $current_attempt,
                    'action_id' => isset($data['action_id']) ? intval($data['action_id']) : 0
                ];
            } else {
                // error_log('Q-Pusher Debug - Time threshold not met yet, notification will be checked again later');
            }
        }

        return $notifications_to_retrigger;
    }
}

/**
 * Retrigger notifications that haven't been clicked
 *
 * @return int Number of notifications retriggered
 */
if (!function_exists('q_retrigger_notifications')) {
    function q_retrigger_notifications()
    {
        // Find notifications that need to be retriggered
        $notifications = q_find_unclicked_notifications();

        if (empty($notifications)) {
            return 0;
        }

        $retriggered_count = 0;

        foreach ($notifications as $notification) {
            $data = $notification['data'];
            $current_attempt = $notification['current_attempt'];

            // Get the user token from the data
            if (!isset($data['token']) || empty($data['token'])) {
                continue;
            }

            // Get the full token from the database using the partial token
            $token_partial = $data['token'];
            $user_id = q_get_user_id_by_token_partial($token_partial);

            if (!$user_id) {
                continue;
            }

            $token = get_user_meta($user_id, 'q_push_token', true);

            if (empty($token)) {
                continue;
            }

            // Prepare notification data for retriggering
            $title = isset($data['title']) ? $data['title'] : 'Notification Reminder';
            $message = isset($data['message']) ? $data['message'] : 'You have an unread notification.';
            $image = isset($data['image']) ? $data['image'] : '';

            // Log the retrigger attempt
            // error_log(sprintf(
            //     'Q-Pusher Debug - Retriggering notification: ID=%s, Attempt=%d/%d',
            //     $notification['notification_id'],
            //     $current_attempt + 1,
            //     isset($data['retrigger_max_attempts']) ? intval($data['retrigger_max_attempts']) : 3
            // ));

            // Increment the attempt counter and add retrigger metadata
            $additional_data = [
                'form_id' => $notification['form_id'],
                'action_id' => $notification['action_id'],
                'enable_retrigger' => true,
                'retrigger_delay' => isset($data['retrigger_delay']) ? intval($data['retrigger_delay']) : 24,
                'retrigger_max_attempts' => isset($data['retrigger_max_attempts']) ? intval($data['retrigger_max_attempts']) : 3,
                'retrigger_attempt' => $current_attempt + 1,
                'is_retrigger' => true,
                'original_notification_id' => $notification['notification_id'],
                'last_retrigger_time' => time() // Store the timestamp of this retrigger
            ];

            // Send the notification
            $result = q_send_push_notification(
                $token,
                $title . ' (Reminder)',
                $message,
                $image,
                false,
                'reminder',
                $additional_data
            );

            if ($result) {
                $retriggered_count++;
                // error_log('Q-Pusher Debug - Successfully sent retrigger notification');
            } else {
                // error_log('Q-Pusher Debug - Failed to send retrigger notification');
            }
        }

        return $retriggered_count;
    }
}

/**
 * Get user ID by partial token
 *
 * @param string $token_partial Partial token (usually first 10 chars followed by '...')
 * @return int|false User ID or false if not found
 */
if (!function_exists('q_get_user_id_by_token_partial')) {
    function q_get_user_id_by_token_partial($token_partial)
    {
        global $wpdb;

        // Extract the actual partial token (remove the '...' if present)
        $clean_partial = str_replace('...', '', $token_partial);

        // Find users with matching token
        $query = $wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta}
            WHERE meta_key = 'q_push_token'
            AND meta_value LIKE %s
            LIMIT 1",
            $clean_partial . '%'
        );

        return $wpdb->get_var($query);
    }
}

// Schedule the retrigger check
if (!function_exists('q_schedule_notification_retrigger')) {
    function q_schedule_notification_retrigger()
    {
        if (!wp_next_scheduled('q_check_notification_retriggers')) {
            wp_schedule_event(time(), 'hourly', 'q_check_notification_retriggers');
            // error_log('Q-Pusher: Scheduled notification retrigger check');
        }
    }
}

// Debug function to check scheduled events
if (!function_exists('q_debug_scheduled_events')) {
    function q_debug_scheduled_events()
    {
        $cron = _get_cron_array();
        $scheduled = wp_next_scheduled('q_check_notification_retriggers');

        // error_log('Q-Pusher Debug - Next scheduled retrigger check: ' .
        //     ($scheduled ? date('Y-m-d H:i:s', $scheduled) : 'Not scheduled'));

        // Check if we have any pending notifications to retrigger
        $notifications = q_find_unclicked_notifications();
        // error_log('Q-Pusher Debug - Found ' . count($notifications) . ' notifications to retrigger');

        foreach ($notifications as $index => $notification) {
            // error_log('Q-Pusher Debug - Notification #' . ($index + 1) . ': ' .
            //     'ID=' . $notification['notification_id'] . ', ' .
            //     'Attempt=' . $notification['current_attempt'] . ', ' .
            //     'Form ID=' . $notification['form_id']);
        }

        return $scheduled;
    }
}

// Hook for the scheduled event
add_action('q_check_notification_retriggers', 'q_retrigger_notifications');

// Add an admin AJAX endpoint for manually triggering notification checks
if (!function_exists('q_manual_check_notifications')) {
    function q_manual_check_notifications()
    {
        // Verify admin permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        // Run the debug function first
        q_debug_scheduled_events();

        // Run the retrigger function
        $count = q_retrigger_notifications();

        wp_send_json_success([
            'message' => sprintf('Manually checked notifications. Retriggered %d notifications.', $count),
            'count' => $count
        ]);
    }
}

// Register the AJAX endpoint
add_action('wp_ajax_q_manual_check_notifications', 'q_manual_check_notifications');

// Add an admin AJAX endpoint for checking notification retrigger history
if (!function_exists('q_check_notification_history')) {
    function q_check_notification_history()
    {
        // Verify admin permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        if (!isset($_POST['notification_id'])) {
            wp_send_json_error('Missing notification ID');
            return;
        }

        $notification_id = sanitize_text_field($_POST['notification_id']);
        $history = q_get_notification_retrigger_history($notification_id);

        wp_send_json_success($history);
    }
}

// Register the history check AJAX endpoint
add_action('wp_ajax_q_check_notification_history', 'q_check_notification_history');

// Schedule the event on plugin activation
register_activation_hook(Q_PLUGIN_DIR . 'q-pusher.php', 'q_schedule_notification_retrigger');

// Unschedule the event on plugin deactivation
register_deactivation_hook(Q_PLUGIN_DIR . 'q-pusher.php', function () {
    wp_clear_scheduled_hook('q_check_notification_retriggers');
});

/**
 * Debug function to get retrigger history for a notification
 *
 * @param string $notification_id The original notification ID
 * @return array Retrigger history
 */
if (!function_exists('q_get_notification_retrigger_history')) {
    function q_get_notification_retrigger_history($notification_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_notification_analytics';

        // Get the original notification
        $original_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE notification_id = %s
            AND type = 'sent'",
            $notification_id
        );

        $original = $wpdb->get_row($original_query);

        if (!$original) {
            return ['error' => 'Original notification not found'];
        }

        // Get all retriggers for this notification
        $retriggers_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE type = 'sent'
            AND data LIKE %s
            ORDER BY timestamp ASC",
            '%"original_notification_id":"' . $notification_id . '"%'
        );

        $retriggers = $wpdb->get_results($retriggers_query);

        // Check if notification was clicked
        $click_query = $wpdb->prepare(
            "SELECT * FROM $table_name
            WHERE notification_id = %s
            AND type = 'click'",
            $notification_id
        );

        $click = $wpdb->get_row($click_query);

        // Format the results
        $history = [
            'original' => [
                'notification_id' => $original->notification_id,
                'timestamp' => $original->timestamp,
                'data' => json_decode($original->data, true)
            ],
            'retriggers' => [],
            'clicked' => !empty($click),
            'click_time' => !empty($click) ? $click->timestamp : null
        ];

        foreach ($retriggers as $retrigger) {
            $data = json_decode($retrigger->data, true);
            $history['retriggers'][] = [
                'notification_id' => $retrigger->notification_id,
                'timestamp' => $retrigger->timestamp,
                'attempt' => isset($data['retrigger_attempt']) ? intval($data['retrigger_attempt']) : 0
            ];
        }

        return $history;
    }
}


if (!function_exists('q_get_pwa_analytics')) {
    function q_get_pwa_analytics($period = 'all', $limit = 10)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return [];
        }

        // Add time period filter
        $where_clause = '';
        if ($period !== 'all') {
            $time_periods = [
                'today' => 'DATE(timestamp) = CURDATE()',
                'yesterday' => 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
                'week' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                'month' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
            ];

            if (isset($time_periods[$period])) {
                $where_clause = 'WHERE ' . $time_periods[$period];
            }
        }

        // Get event counts
        $results = $wpdb->get_results(
            "
            SELECT event_name, COUNT(id) as count
            FROM {$table_name}
            {$where_clause}
            GROUP BY event_name
            ORDER BY count DESC
            " . ($limit > 0 ? "LIMIT {$limit}" : ""),
            ARRAY_A
        );

        if (empty($results)) {
            return [];
        }

        return $results;
    }
}

if (!function_exists('q_pwa_analytics_shortcode')) {
    /**
     * Shortcode to display PWA analytics counts.
     *
     * @param array $atts Shortcode attributes.
     * @return string The event count or an empty string.
     */
    function q_pwa_analytics_shortcode($atts)
    {
        $atts = shortcode_atts(
            array(
                'event_type' => '',
                'period' => 'all', // all, today, yesterday, week, month
                'format' => 'number', // number, percentage, chart
            ),
            $atts,
            'pwa_analytics'
        );

        $event_type = sanitize_text_field($atts['event_type']);
        $period = sanitize_text_field($atts['period']);
        $format = sanitize_text_field($atts['format']);

        if (empty($event_type)) {
            return ''; // or return 'Error: event_type is required.';
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table_name)) != $table_name) {
            return '0';
        }

        // Build the query based on period
        $where_clause = "WHERE event_name = %s";
        $query_params = [$event_type];

        if ($period !== 'all') {
            $time_periods = [
                'today' => 'AND DATE(timestamp) = CURDATE()',
                'yesterday' => 'AND DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
                'week' => 'AND timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
                'month' => 'AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
            ];

            if (isset($time_periods[$period])) {
                $where_clause .= " " . $time_periods[$period];
            }
        }

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(id) FROM {$table_name} {$where_clause}",
            $query_params
        ));

        // Format the output
        if ($format === 'percentage') {
            // Get total events for the period
            $total_query = "SELECT COUNT(id) FROM {$table_name}";
            if ($period !== 'all' && isset($time_periods[$period])) {
                $total_query .= " WHERE " . substr($time_periods[$period], 4); // Remove the "AND " prefix
            }
            $total = $wpdb->get_var($total_query);

            if ($total > 0) {
                $percentage = round(($count / $total) * 100, 1);
                return $percentage . '%';
            }
            return '0%';
        }

        return number_format_i18n($count ?? 0);
    }
}

add_shortcode('pwa_analytics', 'q_pwa_analytics_shortcode');

if (!function_exists('q_holistic_stats_shortcode')) {
    /**
     * Shortcode to display comprehensive PWA analytics (holistic stats).
     *
     * @param array $atts Shortcode attributes.
     * @return string The formatted analytics display.
     */
    function q_holistic_stats_shortcode($atts)
    {
        $atts = shortcode_atts(
            array(
                'period' => 'all', // all, today, yesterday, week, month
                'format' => 'dashboard', // dashboard, table, simple
                'show' => 'all', // all, events, users, devices, engagement
                'style' => 'default', // default, compact, detailed
            ),
            $atts,
            'holistic_stats'
        );

        $period = sanitize_text_field($atts['period']);
        $format = sanitize_text_field($atts['format']);
        $show = sanitize_text_field($atts['show']);
        $style = sanitize_text_field($atts['style']);

        global $wpdb;
        $pwa_table = $wpdb->prefix . 'q_pwa_analytics';
        $notification_table = $wpdb->prefix . 'q_notification_analytics';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$pwa_table'") != $pwa_table) {
            return '<div class="q-analytics-error">Analytics data not available.</div>';
        }

        // Build time period filter
        $time_filter = '';
        $time_periods = [
            'today' => 'DATE(timestamp) = CURDATE()',
            'yesterday' => 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
            'week' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
            'month' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
        ];

        if ($period !== 'all' && isset($time_periods[$period])) {
            $time_filter = 'WHERE ' . $time_periods[$period];
        }

        // Gather comprehensive analytics data
        $analytics_data = [];

        // Total events
        $analytics_data['total_events'] = $wpdb->get_var("SELECT COUNT(*) FROM {$pwa_table} {$time_filter}");

        // Unique users
        $analytics_data['unique_users'] = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM {$pwa_table} {$time_filter} AND user_id > 0");

        // Installations
        $install_filter = $time_filter ? $time_filter . " AND event_name = 'install'" : "WHERE event_name = 'install'";
        $analytics_data['installations'] = $wpdb->get_var("SELECT COUNT(*) FROM {$pwa_table} {$install_filter}");

        // Active sessions
        $session_filter = $time_filter ? $time_filter . " AND event_name IN ('appstart', 'pageview')" : "WHERE event_name IN ('appstart', 'pageview')";
        $analytics_data['active_sessions'] = $wpdb->get_var("SELECT COUNT(DISTINCT session_id) FROM {$pwa_table} {$session_filter}");

        // Device types
        $device_filter = $time_filter ? $time_filter . " AND device_type IS NOT NULL" : "WHERE device_type IS NOT NULL";
        $device_data = $wpdb->get_results("SELECT device_type, COUNT(*) as count FROM {$pwa_table} {$device_filter} GROUP BY device_type ORDER BY count DESC LIMIT 5");
        $analytics_data['devices'] = $device_data;

        // Top events
        $event_data = $wpdb->get_results("SELECT event_name, COUNT(*) as count FROM {$pwa_table} {$time_filter} GROUP BY event_name ORDER BY count DESC LIMIT 5");
        $analytics_data['top_events'] = $event_data;

        // Notification stats (if table exists)
        if ($wpdb->get_var("SHOW TABLES LIKE '$notification_table'") == $notification_table) {
            $notif_filter = $time_filter ? str_replace('timestamp', 'timestamp', $time_filter) : '';
            $analytics_data['notifications_sent'] = $wpdb->get_var("SELECT COUNT(*) FROM {$notification_table} {$notif_filter}");
            $analytics_data['notifications_opened'] = $wpdb->get_var("SELECT COUNT(*) FROM {$notification_table} {$notif_filter}" . ($notif_filter ? " AND" : "WHERE") . " type = 'opened'");
        }

        // Average session duration
        $avg_duration = $wpdb->get_var("SELECT AVG(TIMESTAMPDIFF(SECOND, session_start, session_end)) FROM {$pwa_table} WHERE session_start IS NOT NULL AND session_end IS NOT NULL" . ($time_filter ? " AND " . str_replace('WHERE ', '', $time_filter) : ''));
        $analytics_data['avg_session_duration'] = $avg_duration ? round($avg_duration) : 0;

        // Online users (last 5 minutes)
        $analytics_data['online_users'] = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM {$pwa_table} WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND user_id > 0");

        // Format output based on format parameter
        return q_format_holistic_stats($analytics_data, $format, $show, $style, $period);
    }
}

if (!function_exists('q_format_holistic_stats')) {
    /**
     * Format holistic stats data for display
     */
    function q_format_holistic_stats($data, $format, $show, $style, $period)
    {
        $period_label = ucfirst($period);
        if ($period === 'all') $period_label = 'All Time';

        $output = '<div class="q-holistic-stats q-format-' . esc_attr($format) . ' q-style-' . esc_attr($style) . '">';

        if ($format === 'dashboard') {
            $output .= '<div class="q-stats-header"><h3>PWA Analytics - ' . esc_html($period_label) . '</h3></div>';
            $output .= '<div class="q-stats-grid">';

            if ($show === 'all' || $show === 'events') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['total_events']) . '</div>';
                $output .= '<div class="q-stat-label">Total Events</div>';
                $output .= '</div>';

                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['installations']) . '</div>';
                $output .= '<div class="q-stat-label">Installations</div>';
                $output .= '</div>';
            }

            if ($show === 'all' || $show === 'users') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['unique_users']) . '</div>';
                $output .= '<div class="q-stat-label">Unique Users</div>';
                $output .= '</div>';

                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['online_users']) . '</div>';
                $output .= '<div class="q-stat-label">Online Now</div>';
                $output .= '</div>';
            }

            if ($show === 'all' || $show === 'engagement') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['active_sessions']) . '</div>';
                $output .= '<div class="q-stat-label">Active Sessions</div>';
                $output .= '</div>';

                if (isset($data['avg_session_duration'])) {
                    $duration_formatted = gmdate("H:i:s", $data['avg_session_duration']);
                    $output .= '<div class="q-stat-card">';
                    $output .= '<div class="q-stat-number">' . esc_html($duration_formatted) . '</div>';
                    $output .= '<div class="q-stat-label">Avg Session</div>';
                    $output .= '</div>';
                }
            }

            $output .= '</div>'; // Close grid

            // Additional details for dashboard format
            if ($style === 'detailed') {
                if (($show === 'all' || $show === 'devices') && !empty($data['devices'])) {
                    $output .= '<div class="q-stats-section"><h4>Top Devices</h4><ul>';
                    foreach ($data['devices'] as $device) {
                        $output .= '<li>' . esc_html($device->device_type) . ': ' . number_format_i18n($device->count) . '</li>';
                    }
                    $output .= '</ul></div>';
                }

                if (($show === 'all' || $show === 'events') && !empty($data['top_events'])) {
                    $output .= '<div class="q-stats-section"><h4>Top Events</h4><ul>';
                    foreach ($data['top_events'] as $event) {
                        $output .= '<li>' . esc_html($event->event_name) . ': ' . number_format_i18n($event->count) . '</li>';
                    }
                    $output .= '</ul></div>';
                }
            }
        } elseif ($format === 'table') {
            $output .= '<table class="q-stats-table">';
            $output .= '<caption>PWA Analytics - ' . esc_html($period_label) . '</caption>';
            $output .= '<tbody>';
            $output .= '<tr><td>Total Events</td><td>' . number_format_i18n($data['total_events']) . '</td></tr>';
            $output .= '<tr><td>Installations</td><td>' . number_format_i18n($data['installations']) . '</td></tr>';
            $output .= '<tr><td>Unique Users</td><td>' . number_format_i18n($data['unique_users']) . '</td></tr>';
            $output .= '<tr><td>Online Users</td><td>' . number_format_i18n($data['online_users']) . '</td></tr>';
            $output .= '<tr><td>Active Sessions</td><td>' . number_format_i18n($data['active_sessions']) . '</td></tr>';
            if (isset($data['avg_session_duration'])) {
                $duration_formatted = gmdate("H:i:s", $data['avg_session_duration']);
                $output .= '<tr><td>Avg Session Duration</td><td>' . esc_html($duration_formatted) . '</td></tr>';
            }
            $output .= '</tbody></table>';
        } elseif ($format === 'simple') {
            $output .= '<div class="q-stats-simple">';
            $output .= '<strong>PWA Stats (' . esc_html($period_label) . '):</strong> ';
            $output .= number_format_i18n($data['total_events']) . ' events, ';
            $output .= number_format_i18n($data['installations']) . ' installs, ';
            $output .= number_format_i18n($data['unique_users']) . ' users, ';
            $output .= number_format_i18n($data['online_users']) . ' online';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Add CSS if not already added
        if (!wp_style_is('q-holistic-stats', 'enqueued')) {
            $output .= q_get_holistic_stats_css();
        }

        return $output;
    }
}

if (!function_exists('q_get_holistic_stats_css')) {
    /**
     * Get CSS for holistic stats display
     */
    function q_get_holistic_stats_css()
    {
        return '<style>
        .q-holistic-stats { margin: 20px 0; }
        .q-stats-header h3 { margin: 0 0 15px 0; color: #0073aa; }
        .q-stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .q-stat-card { background: #fff; border: 1px solid #ddd; border-radius: 4px; padding: 15px; text-align: center; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .q-stat-number { font-size: 24px; font-weight: bold; color: #0073aa; margin-bottom: 5px; }
        .q-stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
        .q-stats-section { margin-top: 20px; }
        .q-stats-section h4 { margin: 0 0 10px 0; color: #333; }
        .q-stats-section ul { margin: 0; padding-left: 20px; }
        .q-stats-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .q-stats-table caption { font-weight: bold; margin-bottom: 10px; color: #0073aa; }
        .q-stats-table td { padding: 8px 12px; border-bottom: 1px solid #eee; }
        .q-stats-table td:first-child { font-weight: bold; }
        .q-stats-simple { padding: 15px; background: #f9f9f9; border-left: 4px solid #0073aa; }
        .q-style-compact .q-stat-card { padding: 10px; }
        .q-style-compact .q-stat-number { font-size: 20px; }
        .q-analytics-error { padding: 15px; background: #ffebe8; border: 1px solid #cc0000; color: #cc0000; border-radius: 4px; }
        </style>';
    }
}

add_shortcode('holistic_stats', 'q_holistic_stats_shortcode');

if (!function_exists('q_user_specific_stats_shortcode')) {
    /**
     * Shortcode to display user-specific PWA analytics.
     *
     * @param array $atts Shortcode attributes.
     * @return string The formatted user analytics display.
     */
    function q_user_specific_stats_shortcode($atts)
    {
        $atts = shortcode_atts(
            array(
                'user_id' => '', // Required: user ID to display stats for
                'period' => 'all', // all, today, yesterday, week, month
                'format' => 'dashboard', // dashboard, table, simple, summary
                'show' => 'all', // all, activity, devices, sessions, engagement
                'style' => 'default', // default, compact, detailed
                'current_user' => 'false', // Use current logged-in user if true
            ),
            $atts,
            'user_specific_stats'
        );

        $user_id = sanitize_text_field($atts['user_id']);
        $period = sanitize_text_field($atts['period']);
        $format = sanitize_text_field($atts['format']);
        $show = sanitize_text_field($atts['show']);
        $style = sanitize_text_field($atts['style']);
        $current_user = filter_var($atts['current_user'], FILTER_VALIDATE_BOOLEAN);

        // Use current user if specified
        if ($current_user && is_user_logged_in()) {
            $user_id = get_current_user_id();
        }

        // Validate user_id
        if (empty($user_id) || !is_numeric($user_id)) {
            return '<div class="q-analytics-error">Valid user ID required.</div>';
        }

        global $wpdb;
        $pwa_table = $wpdb->prefix . 'q_pwa_analytics';
        $notification_table = $wpdb->prefix . 'q_notification_analytics';

        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$pwa_table'") != $pwa_table) {
            return '<div class="q-analytics-error">Analytics data not available.</div>';
        }

        // Build time period filter
        $time_filter = '';
        $time_periods = [
            'today' => 'DATE(timestamp) = CURDATE()',
            'yesterday' => 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
            'week' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)',
            'month' => 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)',
        ];

        if ($period !== 'all' && isset($time_periods[$period])) {
            $time_filter = 'AND ' . $time_periods[$period];
        }

        // Gather user-specific analytics data
        $user_data = [];

        // Get user info
        $user_info = get_userdata($user_id);
        $user_data['user_name'] = $user_info ? $user_info->display_name : 'User #' . $user_id;

        // Total events for this user
        $user_data['total_events'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$pwa_table} WHERE user_id = %d {$time_filter}",
            $user_id
        ));

        // Last activity
        $last_activity = $wpdb->get_var($wpdb->prepare(
            "SELECT timestamp FROM {$pwa_table} WHERE user_id = %d ORDER BY timestamp DESC LIMIT 1",
            $user_id
        ));
        $user_data['last_activity'] = $last_activity;

        // Online status (active in last 5 minutes)
        $online_check = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$pwa_table} WHERE user_id = %d AND timestamp >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)",
            $user_id
        ));
        $user_data['is_online'] = $online_check > 0;

        // Device count
        $user_data['device_count'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT device_type) FROM {$pwa_table} WHERE user_id = %d AND device_type IS NOT NULL {$time_filter}",
            $user_id
        ));

        // Device types used
        $device_data = $wpdb->get_results($wpdb->prepare(
            "SELECT device_type, COUNT(*) as count FROM {$pwa_table} WHERE user_id = %d AND device_type IS NOT NULL {$time_filter} GROUP BY device_type ORDER BY count DESC",
            $user_id
        ));
        $user_data['devices'] = $device_data;

        // Session count
        $user_data['session_count'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(DISTINCT session_id) FROM {$pwa_table} WHERE user_id = %d AND session_id IS NOT NULL {$time_filter}",
            $user_id
        ));

        // Average session duration
        $avg_duration = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(TIMESTAMPDIFF(SECOND, session_start, session_end)) FROM {$pwa_table} WHERE user_id = %d AND session_start IS NOT NULL AND session_end IS NOT NULL" . ($time_filter ? " " . $time_filter : ""),
            $user_id
        ));
        $user_data['avg_session_duration'] = $avg_duration ? round($avg_duration) : 0;

        // Most used browser/OS
        $browser_data = $wpdb->get_row($wpdb->prepare(
            "SELECT browser, os, COUNT(*) as count FROM {$pwa_table} WHERE user_id = %d AND browser IS NOT NULL {$time_filter} GROUP BY browser, os ORDER BY count DESC LIMIT 1",
            $user_id
        ));
        $user_data['primary_browser'] = $browser_data;

        // Event breakdown
        $event_data = $wpdb->get_results($wpdb->prepare(
            "SELECT event_name, COUNT(*) as count FROM {$pwa_table} WHERE user_id = %d {$time_filter} GROUP BY event_name ORDER BY count DESC LIMIT 5",
            $user_id
        ));
        $user_data['top_events'] = $event_data;

        // Page views
        $user_data['page_views'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$pwa_table} WHERE user_id = %d AND event_name = 'pageview' {$time_filter}",
            $user_id
        ));

        // App installations by this user
        $user_data['installations'] = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$pwa_table} WHERE user_id = %d AND event_name = 'install' {$time_filter}",
            $user_id
        ));

        // Most visited pages
        $page_data = $wpdb->get_results($wpdb->prepare(
            "SELECT page_url, COUNT(*) as count FROM {$pwa_table} WHERE user_id = %d AND page_url IS NOT NULL {$time_filter} GROUP BY page_url ORDER BY count DESC LIMIT 3",
            $user_id
        ));
        $user_data['top_pages'] = $page_data;

        // Format output based on format parameter
        return q_format_user_specific_stats($user_data, $format, $show, $style, $period);
    }
}

if (!function_exists('q_format_user_specific_stats')) {
    /**
     * Format user-specific stats data for display
     */
    function q_format_user_specific_stats($data, $format, $show, $style, $period)
    {
        $period_label = ucfirst($period);
        if ($period === 'all') $period_label = 'All Time';

        $output = '<div class="q-user-stats q-format-' . esc_attr($format) . ' q-style-' . esc_attr($style) . '">';

        if ($format === 'dashboard') {
            $output .= '<div class="q-stats-header">';
            $output .= '<h3>' . esc_html($data['user_name']) . ' - Analytics (' . esc_html($period_label) . ')</h3>';

            // Online status indicator
            $status_class = $data['is_online'] ? 'online' : 'offline';
            $status_text = $data['is_online'] ? 'Online' : 'Offline';
            $output .= '<div class="q-user-status q-status-' . $status_class . '">' . $status_text . '</div>';
            $output .= '</div>';

            $output .= '<div class="q-stats-grid">';

            if ($show === 'all' || $show === 'activity') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['total_events']) . '</div>';
                $output .= '<div class="q-stat-label">Total Events</div>';
                $output .= '</div>';

                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['page_views']) . '</div>';
                $output .= '<div class="q-stat-label">Page Views</div>';
                $output .= '</div>';
            }

            if ($show === 'all' || $show === 'sessions') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['session_count']) . '</div>';
                $output .= '<div class="q-stat-label">Sessions</div>';
                $output .= '</div>';

                if ($data['avg_session_duration'] > 0) {
                    $duration_formatted = gmdate("H:i:s", $data['avg_session_duration']);
                    $output .= '<div class="q-stat-card">';
                    $output .= '<div class="q-stat-number">' . esc_html($duration_formatted) . '</div>';
                    $output .= '<div class="q-stat-label">Avg Session</div>';
                    $output .= '</div>';
                }
            }

            if ($show === 'all' || $show === 'devices') {
                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['device_count']) . '</div>';
                $output .= '<div class="q-stat-label">Devices Used</div>';
                $output .= '</div>';

                $output .= '<div class="q-stat-card">';
                $output .= '<div class="q-stat-number">' . number_format_i18n($data['installations']) . '</div>';
                $output .= '<div class="q-stat-label">Installations</div>';
                $output .= '</div>';
            }

            $output .= '</div>'; // Close grid

            // Additional details for dashboard format
            if ($style === 'detailed') {
                if ($data['last_activity']) {
                    $last_activity_formatted = human_time_diff(strtotime($data['last_activity']), current_time('timestamp')) . ' ago';
                    $output .= '<div class="q-stats-section"><h4>Last Activity</h4><p>' . esc_html($last_activity_formatted) . '</p></div>';
                }

                if (($show === 'all' || $show === 'devices') && !empty($data['devices'])) {
                    $output .= '<div class="q-stats-section"><h4>Devices</h4><ul>';
                    foreach ($data['devices'] as $device) {
                        $output .= '<li>' . esc_html($device->device_type) . ': ' . number_format_i18n($device->count) . ' events</li>';
                    }
                    $output .= '</ul></div>';
                }

                if (($show === 'all' || $show === 'activity') && !empty($data['top_events'])) {
                    $output .= '<div class="q-stats-section"><h4>Top Activities</h4><ul>';
                    foreach ($data['top_events'] as $event) {
                        $output .= '<li>' . esc_html($event->event_name) . ': ' . number_format_i18n($event->count) . '</li>';
                    }
                    $output .= '</ul></div>';
                }

                if (($show === 'all' || $show === 'activity') && !empty($data['top_pages'])) {
                    $output .= '<div class="q-stats-section"><h4>Most Visited Pages</h4><ul>';
                    foreach ($data['top_pages'] as $page) {
                        $page_name = basename(parse_url($page->page_url, PHP_URL_PATH)) ?: 'Home';
                        $output .= '<li>' . esc_html($page_name) . ': ' . number_format_i18n($page->count) . ' views</li>';
                    }
                    $output .= '</ul></div>';
                }

                if ($data['primary_browser']) {
                    $browser_info = $data['primary_browser']->browser;
                    if ($data['primary_browser']->os) {
                        $browser_info .= ' on ' . $data['primary_browser']->os;
                    }
                    $output .= '<div class="q-stats-section"><h4>Primary Browser</h4><p>' . esc_html($browser_info) . '</p></div>';
                }
            }
        } elseif ($format === 'table') {
            $output .= '<table class="q-stats-table">';
            $output .= '<caption>' . esc_html($data['user_name']) . ' - Analytics (' . esc_html($period_label) . ')</caption>';
            $output .= '<tbody>';
            $output .= '<tr><td>Status</td><td>' . ($data['is_online'] ? 'Online' : 'Offline') . '</td></tr>';
            $output .= '<tr><td>Total Events</td><td>' . number_format_i18n($data['total_events']) . '</td></tr>';
            $output .= '<tr><td>Page Views</td><td>' . number_format_i18n($data['page_views']) . '</td></tr>';
            $output .= '<tr><td>Sessions</td><td>' . number_format_i18n($data['session_count']) . '</td></tr>';
            $output .= '<tr><td>Devices Used</td><td>' . number_format_i18n($data['device_count']) . '</td></tr>';
            $output .= '<tr><td>Installations</td><td>' . number_format_i18n($data['installations']) . '</td></tr>';
            if ($data['avg_session_duration'] > 0) {
                $duration_formatted = gmdate("H:i:s", $data['avg_session_duration']);
                $output .= '<tr><td>Avg Session Duration</td><td>' . esc_html($duration_formatted) . '</td></tr>';
            }
            if ($data['last_activity']) {
                $last_activity_formatted = human_time_diff(strtotime($data['last_activity']), current_time('timestamp')) . ' ago';
                $output .= '<tr><td>Last Activity</td><td>' . esc_html($last_activity_formatted) . '</td></tr>';
            }
            $output .= '</tbody></table>';
        } elseif ($format === 'simple') {
            $status = $data['is_online'] ? 'Online' : 'Offline';
            $output .= '<div class="q-stats-simple">';
            $output .= '<strong>' . esc_html($data['user_name']) . ' (' . esc_html($period_label) . '):</strong> ';
            $output .= $status . ', ' . number_format_i18n($data['total_events']) . ' events, ';
            $output .= number_format_i18n($data['page_views']) . ' views, ';
            $output .= number_format_i18n($data['session_count']) . ' sessions';
            $output .= '</div>';
        } elseif ($format === 'summary') {
            $status_class = $data['is_online'] ? 'online' : 'offline';
            $output .= '<div class="q-user-summary">';
            $output .= '<div class="q-user-info">';
            $output .= '<span class="q-user-name">' . esc_html($data['user_name']) . '</span>';
            $output .= '<span class="q-user-status q-status-' . $status_class . '">' . ($data['is_online'] ? 'Online' : 'Offline') . '</span>';
            $output .= '</div>';
            $output .= '<div class="q-user-metrics">';
            $output .= '<span>' . number_format_i18n($data['total_events']) . ' events</span>';
            $output .= '<span>' . number_format_i18n($data['session_count']) . ' sessions</span>';
            $output .= '</div>';
            $output .= '</div>';
        }

        $output .= '</div>';

        // Add CSS if not already added
        if (!wp_style_is('q-user-stats', 'enqueued')) {
            $output .= q_get_user_stats_css();
        }

        return $output;
    }
}

if (!function_exists('q_get_user_stats_css')) {
    /**
     * Get CSS for user-specific stats display
     */
    function q_get_user_stats_css()
    {
        return '<style>
        .q-user-stats { margin: 20px 0; }
        .q-user-stats .q-stats-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .q-user-stats .q-stats-header h3 { margin: 0; color: #0073aa; }
        .q-user-status { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .q-status-online { background: #d4edda; color: #155724; }
        .q-status-offline { background: #f8d7da; color: #721c24; }
        .q-user-summary { display: flex; justify-content: space-between; align-items: center; padding: 15px; background: #f9f9f9; border-radius: 4px; }
        .q-user-info { display: flex; align-items: center; gap: 10px; }
        .q-user-name { font-weight: bold; }
        .q-user-metrics { display: flex; gap: 15px; font-size: 14px; color: #666; }
        .q-user-metrics span { white-space: nowrap; }
        </style>';
    }
}

add_shortcode('user_specific_stats', 'q_user_specific_stats_shortcode');

/**
 * AJAX handler for tracking PWA events
 */
if (!function_exists('q_pwa_track_event')) {
    function q_pwa_track_event()
    {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'q_pwa_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check if analytics is enabled
        if (!get_option('q_pwa_analytics_enabled', true)) {
            wp_send_json_success('Analytics disabled');
            return;
        }

        // Get event data
        $event_data = isset($_POST['event_data']) ? json_decode(stripslashes($_POST['event_data']), true) : [];

        if (empty($event_data) || !isset($event_data['event_name'])) {
            wp_send_json_error('Invalid event data');
            return;
        }

        // Sanitize data
        $event_name = sanitize_text_field($event_data['event_name']);
        $user_id = isset($event_data['user_id']) ? sanitize_text_field($event_data['user_id']) : '';
        $device_type = isset($event_data['device_type']) ? sanitize_text_field($event_data['device_type']) : '';

        // Extract additional fields for enhanced storage
        $os = isset($event_data['os']) ? sanitize_text_field($event_data['os']) : '';
        $browser = isset($event_data['browser']) ? sanitize_text_field($event_data['browser']) : '';
        $app_version = isset($event_data['app_version']) ? sanitize_text_field($event_data['app_version']) : '';
        $session_id = isset($event_data['session_id']) ? sanitize_text_field($event_data['session_id']) : '';
        $page_url = isset($event_data['url']) ? esc_url_raw($event_data['url']) : '';

        // Handle session timing data
        $session_start = null;
        $session_end = null;
        if ($event_name === 'session_start') {
            $session_start = current_time('mysql');
        } elseif ($event_name === 'session_end' && isset($event_data['session_duration'])) {
            $session_end = current_time('mysql');
            // Calculate session start time from duration
            $duration_seconds = intval($event_data['session_duration']) / 1000; // Convert from milliseconds
            $session_start = date('Y-m-d H:i:s', current_time('timestamp') - $duration_seconds);
        }

        // Remove fields that are now stored in dedicated columns
        unset($event_data['event_name']);
        unset($event_data['user_id']);
        unset($event_data['device_type']);
        unset($event_data['os']);
        unset($event_data['browser']);
        unset($event_data['app_version']);
        unset($event_data['session_id']);
        unset($event_data['url']);
        unset($event_data['timestamp']); // Use server timestamp instead
        unset($event_data['session_duration']); // Handled above

        // Sanitize any remaining data
        $data = !empty($event_data) ? json_encode($event_data) : null;

        // Store in database
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        /* ------------------------------------------------------------------
         * Schema safeguard: Older installations may lack one or more columns
         * that this handler now relies on (e.g., device_type or event_data).
         * To avoid "unknown column" SQL errors, we verify the schema at
         * runtime and trigger the dbDelta-powered upgrade if needed.
         * ------------------------------------------------------------------ */
        $existing_columns = $wpdb->get_col("DESCRIBE {$table_name}");
        $required_columns = ['device_type', 'event_data', 'os', 'browser', 'app_version', 'session_id', 'session_start', 'session_end', 'page_url', 'ip_address', 'user_agent'];
        $needs_upgrade = false;

        foreach ($required_columns as $col) {
            if (!in_array($col, $existing_columns, true)) {
                $needs_upgrade = true;
                break;
            }
        }

        if ($needs_upgrade && class_exists('Q_PWA_Analytics')) {
            Q_PWA_Analytics::create_tables(); // dbDelta will add any missing columns without data loss
        }

        // Prepare insert data with enhanced fields
        $insert_data = [
            'event_name' => $event_name,
            'user_id' => $user_id ?: get_current_user_id(), // Use WordPress user ID if available
            'device_type' => $device_type,
            'os' => $os,
            'browser' => $browser,
            'app_version' => $app_version,
            'session_id' => $session_id,
            'session_start' => $session_start,
            'session_end' => $session_end,
            'page_url' => $page_url,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'event_data' => $data
        ];

        $insert_formats = [
            '%s', // event_name
            '%d', // user_id
            '%s', // device_type
            '%s', // os
            '%s', // browser
            '%s', // app_version
            '%s', // session_id
            '%s', // session_start
            '%s', // session_end
            '%s', // page_url
            '%s', // ip_address
            '%s', // user_agent
            '%s'  // event_data
        ];

        $result = $wpdb->insert($table_name, $insert_data, $insert_formats);

        if ($result) {
            wp_send_json_success('Event tracked');
        } else {
            wp_send_json_error('Failed to track event');
        }
    }
}

// Register AJAX handlers
add_action('wp_ajax_q_pwa_track_event', 'q_pwa_track_event');
add_action('wp_ajax_nopriv_q_pwa_track_event', 'q_pwa_track_event');

/**
 * Get PWA analytics data for time-based charts
 * 
 * @param string $event_type Event type to filter
 * @param string $period Period to analyze (day, week, month)
 * @param int $points Number of data points to return
 * @return array Analytics data for charting
 */
if (!function_exists('q_get_pwa_analytics_chart_data')) {
    function q_get_pwa_analytics_chart_data($event_type = '', $period = 'week', $points = 7)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'q_pwa_analytics';

        // Check if the table exists to prevent errors
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            return [];
        }

        $where_clause = '';
        $group_by = '';
        $date_format = '';

        switch ($period) {
            case 'day':
                $date_format = '%H:00'; // Hours in a day
                $group_by = 'HOUR(timestamp)';
                $where_clause = 'WHERE DATE(timestamp) = CURDATE()';
                $points = min($points, 24); // Max 24 hours in a day
                break;

            case 'week':
                $date_format = '%a'; // Day name
                $group_by = 'DAYOFWEEK(timestamp)';
                $where_clause = 'WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                $points = min($points, 7); // Max 7 days in a week
                break;

            case 'month':
                $date_format = '%d'; // Day of month
                $group_by = 'DAY(timestamp)';
                $where_clause = 'WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                $points = min($points, 31); // Max 31 days in a month
                break;

            default:
                return [];
        }

        // Add event type filter if provided
        if (!empty($event_type)) {
            $where_clause .= $where_clause ? ' AND ' : 'WHERE ';
            $where_clause .= $wpdb->prepare('event_name = %s', $event_type);
        }

        // Get the data
        $results = $wpdb->get_results($wpdb->prepare("
            SELECT 
                DATE_FORMAT(timestamp, %s) as label,
                {$group_by} as period_value,
                COUNT(id) as count
            FROM {$table_name}
            {$where_clause}
            GROUP BY period_value
            ORDER BY period_value ASC
            LIMIT %d
        ", $date_format, $points), ARRAY_A);

        return $results;
    }
}
