<?php
/**
 * Test file for PWA Analytics functionality
 * 
 * This file can be used to test the new analytics shortcodes and functionality.
 * Place this file in your WordPress root directory and access it via browser.
 * 
 * Usage: http://yoursite.com/test-analytics.php
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Check if user is logged in and is admin
if (!is_user_logged_in() || !current_user_can('manage_options')) {
    wp_die('Access denied. Please log in as an administrator.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Analytics Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h2 { color: #0073aa; margin-top: 0; }
        .shortcode-example { background: #f9f9f9; padding: 10px; border-left: 4px solid #0073aa; margin: 10px 0; }
        .shortcode-example code { background: #fff; padding: 2px 4px; border-radius: 3px; }
        .test-buttons { margin: 20px 0; }
        .test-buttons button { margin: 5px; padding: 10px 15px; background: #0073aa; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-buttons button:hover { background: #005a87; }
        .analytics-output { margin: 20px 0; }
        .error { color: #d63638; background: #fcf0f1; padding: 10px; border-radius: 3px; }
        .success { color: #00a32a; background: #f0f6fc; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>PWA Analytics Test Page</h1>
    <p>This page tests the new PWA analytics shortcodes and functionality.</p>

    <div class="test-section">
        <h2>1. Holistic Stats Shortcode Test</h2>
        <p>Testing the <code>[holistic_stats]</code> shortcode with different parameters:</p>
        
        <div class="shortcode-example">
            <h3>Dashboard Format (Default)</h3>
            <code>[holistic_stats period="week" format="dashboard" style="detailed"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[holistic_stats period="week" format="dashboard" style="detailed"]'); ?>
            </div>
        </div>

        <div class="shortcode-example">
            <h3>Table Format</h3>
            <code>[holistic_stats period="month" format="table"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[holistic_stats period="month" format="table"]'); ?>
            </div>
        </div>

        <div class="shortcode-example">
            <h3>Simple Format</h3>
            <code>[holistic_stats period="today" format="simple"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[holistic_stats period="today" format="simple"]'); ?>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>2. User-Specific Stats Shortcode Test</h2>
        <p>Testing the <code>[user_specific_stats]</code> shortcode:</p>
        
        <div class="shortcode-example">
            <h3>Current User Dashboard</h3>
            <code>[user_specific_stats current_user="true" format="dashboard" style="detailed"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[user_specific_stats current_user="true" format="dashboard" style="detailed"]'); ?>
            </div>
        </div>

        <div class="shortcode-example">
            <h3>Current User Summary</h3>
            <code>[user_specific_stats current_user="true" format="summary"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[user_specific_stats current_user="true" format="summary"]'); ?>
            </div>
        </div>

        <div class="shortcode-example">
            <h3>Specific User (ID: 1)</h3>
            <code>[user_specific_stats user_id="1" period="week" format="table"]</code>
            <div class="analytics-output">
                <?php echo do_shortcode('[user_specific_stats user_id="1" period="week" format="table"]'); ?>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Analytics Tracking Test</h2>
        <p>Test the enhanced analytics tracking by interacting with the elements below:</p>
        
        <div class="test-buttons">
            <button onclick="testButtonClick()">Test Button Click</button>
            <button onclick="testFormSubmit()">Test Form Submit</button>
            <button onclick="testCustomEvent()">Test Custom Event</button>
            <a href="#test" onclick="testLinkClick(event)">Test Link Click</a>
        </div>

        <form id="test-form" onsubmit="return false;">
            <h4>Test Form</h4>
            <input type="text" name="test_field" placeholder="Test input field" />
            <textarea name="test_textarea" placeholder="Test textarea"></textarea>
            <select name="test_select">
                <option value="">Select option</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
            </select>
            <button type="submit">Submit Test Form</button>
        </form>

        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>4. Database Check</h2>
        <p>Checking analytics database tables and recent data:</p>
        
        <?php
        global $wpdb;
        $pwa_table = $wpdb->prefix . 'q_pwa_analytics';
        $notification_table = $wpdb->prefix . 'q_notification_analytics';
        
        // Check if tables exist
        $pwa_exists = $wpdb->get_var("SHOW TABLES LIKE '$pwa_table'") == $pwa_table;
        $notification_exists = $wpdb->get_var("SHOW TABLES LIKE '$notification_table'") == $notification_table;
        
        echo '<div class="' . ($pwa_exists ? 'success' : 'error') . '">';
        echo 'PWA Analytics Table: ' . ($pwa_exists ? 'EXISTS' : 'MISSING');
        echo '</div>';
        
        echo '<div class="' . ($notification_exists ? 'success' : 'error') . '">';
        echo 'Notification Analytics Table: ' . ($notification_exists ? 'EXISTS' : 'MISSING');
        echo '</div>';
        
        if ($pwa_exists) {
            $recent_events = $wpdb->get_results("SELECT event_name, COUNT(*) as count FROM $pwa_table WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) GROUP BY event_name ORDER BY count DESC LIMIT 10");
            
            if ($recent_events) {
                echo '<h4>Recent Events (Last 24 Hours):</h4>';
                echo '<ul>';
                foreach ($recent_events as $event) {
                    echo '<li>' . esc_html($event->event_name) . ': ' . number_format($event->count) . ' events</li>';
                }
                echo '</ul>';
            } else {
                echo '<p>No recent events found in the last 24 hours.</p>';
            }
            
            // Check table schema
            $columns = $wpdb->get_results("DESCRIBE $pwa_table");
            echo '<h4>PWA Analytics Table Schema:</h4>';
            echo '<ul>';
            foreach ($columns as $column) {
                echo '<li><strong>' . esc_html($column->Field) . '</strong>: ' . esc_html($column->Type) . '</li>';
            }
            echo '</ul>';
        }
        ?>
    </div>

    <script>
        // Test functions for analytics tracking
        function testButtonClick() {
            if (window.qPWATrack) {
                window.qPWATrack('test_button_click', { test: true });
                showResult('Button click event tracked!');
            } else {
                showResult('Analytics tracking not available', 'error');
            }
        }

        function testFormSubmit() {
            if (window.qPWATrack) {
                window.qPWATrack('test_form_submit', { form_id: 'test-form' });
                showResult('Form submit event tracked!');
            } else {
                showResult('Analytics tracking not available', 'error');
            }
        }

        function testCustomEvent() {
            if (window.qPWATrack) {
                window.qPWATrack('custom_test_event', { 
                    timestamp: new Date().toISOString(),
                    page: 'test-analytics'
                });
                showResult('Custom event tracked!');
            } else {
                showResult('Analytics tracking not available', 'error');
            }
        }

        function testLinkClick(event) {
            event.preventDefault();
            if (window.qPWATrack) {
                window.qPWATrack('test_link_click', { href: event.target.href });
                showResult('Link click event tracked!');
            } else {
                showResult('Analytics tracking not available', 'error');
            }
        }

        function showResult(message, type = 'success') {
            const resultsDiv = document.getElementById('test-results');
            const resultElement = document.createElement('div');
            resultElement.className = type;
            resultElement.textContent = message;
            resultsDiv.appendChild(resultElement);
            
            // Remove after 3 seconds
            setTimeout(() => {
                resultElement.remove();
            }, 3000);
        }

        // Check if analytics tracking is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (window.qPWATrack) {
                showResult('Analytics tracking is loaded and ready!');
            } else {
                showResult('Analytics tracking is not loaded', 'error');
            }
        });
    </script>

    <?php wp_footer(); ?>
</body>
</html>
