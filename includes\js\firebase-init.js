// Validate Firebase configuration
const validateConfig = (config) => {
    if (!config) {
        console.error('Firebase configuration is missing');
        return false;
    }

    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    const missingFields = requiredFields.filter(field => !config[field]);

    if (missingFields.length > 0) {
        console.error('Firebase configuration is missing required fields:', missingFields);
        return false;
    }

    return true;
};

// Initialize Firebase with error handling
let app, messaging;

// Create a promise that will resolve with the messaging instance
let messagingResolve;
const messagingPromise = new Promise(resolve => {
    messagingResolve = resolve;
});

// Add a version query parameter to bust browser and proxy caches whenever the script is changed.
// Increase the value whenever you deploy a new service-worker build.
const Q_SW_VERSION = '20240625';

document.addEventListener('DOMContentLoaded', function() {
    try {
        // Load Firebase modules dynamically
        const loadFirebase = async () => {
            try {
                // Dynamic imports for Firebase modules
                const firebaseApp = await import("https://www.gstatic.com/firebasejs/11.5.0/firebase-app.js");
                const firebaseMessaging = await import("https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js");
                
                const { initializeApp } = firebaseApp;
                const { getMessaging } = firebaseMessaging;
                
                if (!validateConfig(window.q_firebase_config)) {
                    throw new Error('Invalid Firebase configuration');
                }
                
                app = initializeApp(window.q_firebase_config);
                messaging = getMessaging(app);
                if (window.location.hostname === 'localhost') {
                    console.log('Firebase initialized successfully with project:', window.q_firebase_config.projectId);
                }
                
                // Register service worker
                if ('serviceWorker' in navigator) {
                    const swUrl = `/firebase-messaging-sw.js?v=${Q_SW_VERSION}`;

                    // Helper to show user-friendly offline message
                    function showOfflineMessage() {
                        // Replace this with your own UI logic as needed
                        if (!document.getElementById('pwa-offline-message')) {
                            const msg = document.createElement('div');
                            msg.id = 'pwa-offline-message';
                            msg.style.position = 'fixed';
                            msg.style.bottom = '20px';
                            msg.style.left = '50%';
                            msg.style.transform = 'translateX(-50%)';
                            msg.style.background = '#ff9800';
                            msg.style.color = '#fff';
                            msg.style.padding = '12px 24px';
                            msg.style.borderRadius = '6px';
                            msg.style.zIndex = '9999';
                            msg.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
                            msg.textContent = 'You are offline. Push notifications will be enabled when you are back online.';
                            document.body.appendChild(msg);
                        }
                    }
                    function hideOfflineMessage() {
                        const msg = document.getElementById('pwa-offline-message');
                        if (msg) msg.remove();
                    }

                    // Retry registration when back online
                    let onlineListener = null;
                    function setupOnlineRetry() {
                        if (!onlineListener) {
                            onlineListener = async () => {
                                if (window.location.hostname === 'localhost') {
                                    console.log('Network connection restored. Retrying service worker registration...');
                                }
                                hideOfflineMessage();
                                window.removeEventListener('online', onlineListener);
                                onlineListener = null;
                                // Retry registration
                                tryRegisterServiceWorker();
                            };
                            window.addEventListener('online', onlineListener);
                        }
                    }

                    async function tryRegisterServiceWorker() {
                        if (!navigator.onLine) {
                            showOfflineMessage();
                            setupOnlineRetry();
                            messagingResolve(null);
                            return;
                        }
                        hideOfflineMessage();
                        try {
                            // Check if the service worker file is accessible
                            const response = await fetch(swUrl, { method: 'HEAD', cache: 'no-store' });
                            if (!response.ok) {
                                throw new Error('Service worker file not found at /firebase-messaging-sw.js. Please ensure it exists at the site root.');
                            }
                            // Proceed with registration
                            const registration = await navigator.serviceWorker.register(swUrl, {
                                scope: '/',
                                type: 'classic',
                                updateViaCache: 'none'
                            });
                            if (window.location.hostname === 'localhost') {
                                console.log('Service worker registration successful:', registration);
                            }
                            // Periodically check for new service workers every hour
                            setInterval(() => {
                                registration.update();
                            }, 60 * 60 * 1000);
                            // Listen for controllerchange to reload the page
                            navigator.serviceWorker.addEventListener('controllerchange', () => {
                                console.log('New Service Worker activated, reloading page...');
                                window.location.reload();
                            });
                            if (!navigator.onLine) {
                                console.warn('Network is offline. Service worker may use cached version.');
                                showOfflineMessage();
                                setupOnlineRetry();
                                messagingResolve(null);
                                return;
                            }
                            try {
                                await registration.update();
                            } catch (err) {
                                console.warn('Service worker update failed, but continuing:', err);
                            }
                            if (window.location.hostname === 'localhost') {
                                console.log('Service Worker updated successfully');
                            }
                            window.qMessaging = messaging;
                            messagingResolve(messaging);
                        } catch (err) {
                            console.error('Failed to register service worker or initialize messaging:', err);
                            if (!navigator.onLine) {
                                showOfflineMessage();
                                setupOnlineRetry();
                            } else {
                                alert('You are offline. Push notifications will be enabled when you are back online.');
                            }
                            messagingResolve(null);
                        }
                    }

                    // Initial attempt
                    tryRegisterServiceWorker();
                } else {
                    console.error('Service workers are not supported in this browser');
                    messagingResolve(null);
                }
                
                // Make messaging available globally
                window.qFirebase = {
                    app,
                    messaging
                };
                
            } catch (error) {
                console.error('Failed to load Firebase modules:', error);
                messagingResolve(null);
            }
        };
        
        loadFirebase();
        
    } catch (error) {
        console.error('Failed to initialize Firebase:', error);
        messagingResolve(null);
    }
});

// Export the messaging promise for other modules to use
export { messagingPromise };