# CHANGELOG

## Unreleased

## 1.6.0 - 2025-04-07

* Use conditional PHPStan return type to correctly type hint an array when using `JSON::decode()` and 
  `JSON::decodeFile()`

## 1.5.0 - 2024-12-19

* Remove requirement that a string must be non-empty

## 1.5.0 - 2024-08-17

* Added support for PHP 8.4

## 1.4.0 - 2023-11-30

* Restored support for PHP 8.1

## 1.3.0 - 2023-11-25

* Added support for PHP 8.3, dropped support for PHP 8.1

## 1.2.1 - 2022-12-19

Fixed symlinks not being properly resolved

## 1.2.0 - 2022-12-01

Added more checks when decoding JSON Files

## 1.1.0 - 2022-11-04

Dropped support for PHP <8.1

## 1.0.0 - 2022-02-12

Initial release
