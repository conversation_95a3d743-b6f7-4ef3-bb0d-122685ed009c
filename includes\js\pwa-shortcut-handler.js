/**
 * PWA Shortcut Handler
 * 
 * Handles PWA shortcuts and prevents them from opening in new windows
 * Enhanced with analytics tracking and improved user experience
 */

// Execute immediately to prevent new window opening
(function() {
    // Check if this is a PWA shortcut request
    const urlParams = new URLSearchParams(window.location.search);
    const isPwaShortcut = urlParams.get('pwa_shortcut') === '1';
    
    if (isPwaShortcut) {
        // Track shortcut usage
        trackShortcutUsage();
        
        // Check if this is a PWA
        const isPwa = window.matchMedia('(display-mode: standalone)').matches || 
                     window.navigator.standalone === true;
        
        // If not in PWA context, try to find an existing PWA window
        if (!isPwa && window.opener) {
            try {
                // Try to close this window after a short delay
                setTimeout(function() {
                    window.close();
                }, 100);
            } catch (e) {
                console.error('Failed to handle PWA shortcut:', e);
            }
        }
        
        // Clean up the URL by removing the pwa_shortcut parameter
        if (window.history && window.history.replaceState) {
            const cleanUrl = window.location.href.replace(/[?&]pwa_shortcut=1/, '');
            window.history.replaceState({}, document.title, cleanUrl);
        }
    }
    
    // Listen for shortcut messages from service worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'pwa_shortcut') {
                if (window.location.hostname === 'localhost') {
                    console.log('Shortcut activated:', event.data.name || 'Unknown shortcut');
                }
                
                // Track the shortcut usage
                if (event.data.name) {
                    trackShortcutUsage(event.data.name);
                }
            }
        });
    }
    
    // Track shortcut usage with analytics
    function trackShortcutUsage(shortcutName) {
        // Check if analytics is available
        if (typeof gtag === 'function') {
            gtag('event', 'pwa_shortcut_used', {
                'shortcut_name': shortcutName || 'unknown',
                'page_path': window.location.pathname
            });
        }
        
        // Send tracking data to WordPress if available
        if (typeof jQuery === 'function' && typeof ajaxurl !== 'undefined') {
            jQuery.post(ajaxurl, {
                action: 'q_track_pwa_shortcut',
                shortcut_name: shortcutName || 'unknown',
                page_path: window.location.pathname
            });
        }
    }
})();

// Register for handling shortcuts when in PWA mode
if ('serviceWorker' in navigator && window.matchMedia('(display-mode: standalone)').matches) {
    window.addEventListener('load', function() {
        // Tell the service worker this client can handle shortcuts
        navigator.serviceWorker.ready.then(registration => {
            if (registration.active) {
                registration.active.postMessage({
                    type: 'CAN_HANDLE_SHORTCUTS'
                });
            }
        });
    });
}